// 布局模块

import { StateCreator } from 'zustand';
import { Element, Page } from './types';
import { PageSlice } from './pageSlice';
import { HistorySlice } from './historySlice';
import { SelectionSlice } from './selectionSlice';
import { getDefaultWidthForElement, getDefaultHeightForElement } from '../utils/collisionDetection';
import { ensureArray, ensureId } from './utils/typeUtils';

// 安全地访问数组元素
function safeArrayAccess<T>(array: T[] | null | undefined, index: number, defaultValue: T | null): T | null {
  if (!array || index < 0 || index >= array.length) {
    return defaultValue;
  }
  return array[index] || defaultValue;
}

// 布局状态接口
export interface LayoutSlice {
  // 布局方法
  arrangeElementsWaterfall: (specifiedColumns?: number) => void;
  alignElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => void;
  distributeElements: (distributeType: 'horizontal' | 'vertical') => void;
}

// 定义组合状态类型，用于reducer函数的类型注解
type CombinedState = LayoutSlice & PageSlice & HistorySlice & SelectionSlice;

// 创建布局状态
export const createLayoutSlice: StateCreator<
  CombinedState,
  [],
  [],
  LayoutSlice
> = (set, get) => {
  return {
    // 瀑布流排列
    arrangeElementsWaterfall: (specifiedColumns?: number) => set((state: CombinedState) => {
      const { pages, currentPageId, questionsPerPage } = state;
      const currentPageIndex: number = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage: Page | null = safeArrayAccess(pages, currentPageIndex, null);
      if (!currentPage) return state;
      
      const elements: Element[] = [...ensureArray(currentPage.elements)];
      
      // 保存原始元素状态，用于历史记录
      const previousElements: Element[] = JSON.parse(JSON.stringify(elements));
      
      // 定义布局常量
      const MARGIN_TOP: number = 35; // 顶部边距（mm）
      const MARGIN_LEFT: number = 20; // 左侧边距（mm）
      const PAGE_WIDTH: number = 210; // A4纸宽度（mm）
      const PAGE_HEIGHT: number = 297; // A4纸高度（mm）
      const PAGE_MARGIN_BOTTOM: number = 20; // 底部边距（mm）
      const ELEMENT_SPACING: number = questionsPerPage === 0 ? 1 : 2; // 元素间距（mm）
      
      // 按Y坐标排序，保持元素的相对位置
      elements.sort((a: Element, b: Element) => {
        // 标题始终在最前
        if (a.type === 'title') return -1;
        if (b.type === 'title') return 1;
        
        // 小标题排在标题之后，但在其他元素之前
        if (a.type === 'subtitle' && b.type !== 'subtitle') return -1;
        if (b.type === 'subtitle' && a.type !== 'subtitle') return 1;
        
        // 其他元素按当前Y坐标排序
        return a.position.y - b.position.y;
      });
      
      // 分离标题、副标题和其他元素
      const titleElements: Element[] = elements.filter(el => el.type === 'title');
      const subtitleElements: Element[] = elements.filter(el => el.type === 'subtitle');
      const contentElements: Element[] = elements.filter(el => 
        el.type !== 'title' && el.type !== 'subtitle'
      );
      
      // 计算可用于瀑布流的列数
      let columnCount: number = specifiedColumns || 
        (PAGE_WIDTH >= 210 ? (questionsPerPage === 0 ? 2 : 1) : 1);
      
      // 对标题元素进行排列
      let yPosition: number = MARGIN_TOP;
      titleElements.forEach((element: Element) => {
        element.position = {
          x: MARGIN_LEFT,
          y: yPosition
        };
        yPosition += (element.size?.height || 30) + ELEMENT_SPACING;
      });
      
      // 对副标题元素进行排列
      subtitleElements.forEach((element: Element) => {
        element.position = {
          x: MARGIN_LEFT,
          y: yPosition
        };
        yPosition += (element.size?.height || 20) + ELEMENT_SPACING;
      });
      
      // 初始化列高度数组
      const columnHeights: number[] = Array(columnCount).fill(yPosition + ELEMENT_SPACING * 2);
      const columnWidth: number = (PAGE_WIDTH - MARGIN_LEFT * 2) / columnCount;
      
      // 对内容元素进行瀑布流排列
      contentElements.forEach((element: Element) => {
        // 找到高度最小的列
        const minHeightIndex: number = columnHeights.findIndex(h => h === Math.min(...columnHeights));
        const minHeightColumn: number = minHeightIndex !== -1 ? minHeightIndex : 0;
        
        // 计算元素在该列中的位置
        const elementX: number = MARGIN_LEFT + minHeightColumn * columnWidth;
        const elementY: number = columnHeights[minHeightColumn] || yPosition;
        
        // 更新元素位置
        element.position = {
          x: elementX,
          y: elementY
        };
        
        // 更新该列的高度
        const elementHeight: number = element.size?.height || 
          (element.type === 'image' ? 105 : 15);
        
        columnHeights[minHeightColumn] = (columnHeights[minHeightColumn] || yPosition) + elementHeight + ELEMENT_SPACING;
      });
      
      // 更新页面元素
      const updatedPages: Page[] = [...pages];
      if (currentPage && currentPageIndex >= 0 && currentPageIndex < updatedPages.length) {
        updatedPages[currentPageIndex] = {
          ...currentPage,
          id: ensureId(currentPage.id),
          elements
        };
      }
      
      // 添加到历史记录
      const historyAction = {
        type: 'arrangement' as const,
        pageId: currentPageId,
        elements,
        previousElements
      };
      
      const addToHistory = get().addToHistory;
      if (addToHistory) {
        addToHistory(historyAction);
      }
      
      return { pages: updatedPages };
    }),
    
    // 对齐元素
    alignElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => set((state: CombinedState) => {
      const { pages, currentPageId, selectedElementIds } = state;
      if (selectedElementIds.length <= 1) return state;
      
      const currentPageIndex: number = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage: Page | null = safeArrayAccess(pages, currentPageIndex, null);
      if (!currentPage) return state;
      
      const selectedElements: Element[] = ensureArray(currentPage.elements).filter(el => 
        selectedElementIds.includes(el.id)
      );
      
      if (selectedElements.length <= 1) return state;
      
      // 保存原始元素状态，用于历史记录
      const previousElements: Element[] = JSON.parse(JSON.stringify(selectedElements));
      
      // 根据对齐类型计算目标位置
      switch (alignType) {
        case 'left': {
          // 找到最左侧的元素
          const leftmost: number = Math.min(...selectedElements.map(el => el.position.x));
          selectedElements.forEach((el: Element) => {
            el.position.x = leftmost;
          });
          break;
        }
        case 'center': {
          // 计算水平中心点
          const centerValues: number[] = selectedElements.map((el: Element) => {
            const width: number = el.size?.width || getDefaultWidthForElement(el);
            return el.position.x + width / 2;
          });
          const centerX: number = centerValues.reduce((sum, val) => sum + val, 0) / centerValues.length;
          
          selectedElements.forEach((el: Element) => {
            const width: number = el.size?.width || getDefaultWidthForElement(el);
            el.position.x = centerX - width / 2;
          });
          break;
        }
        case 'right': {
          // 找到最右侧的元素
          const rightPositions: number[] = selectedElements.map((el: Element) => {
            const width: number = el.size?.width || getDefaultWidthForElement(el);
            return el.position.x + width;
          });
          const rightmost: number = Math.max(...rightPositions);
          
          selectedElements.forEach((el: Element) => {
            const width: number = el.size?.width || getDefaultWidthForElement(el);
            el.position.x = rightmost - width;
          });
          break;
        }
        case 'top': {
          // 找到最顶部的元素
          const topmost: number = Math.min(...selectedElements.map(el => el.position.y));
          selectedElements.forEach((el: Element) => {
            el.position.y = topmost;
          });
          break;
        }
        case 'middle': {
          // 计算垂直中心点
          const middleValues: number[] = selectedElements.map((el: Element) => {
            const height: number = el.size?.height || getDefaultHeightForElement(el);
            return el.position.y + height / 2;
          });
          const middleY: number = middleValues.reduce((sum, val) => sum + val, 0) / middleValues.length;
          
          selectedElements.forEach((el: Element) => {
            const height: number = el.size?.height || getDefaultHeightForElement(el);
            el.position.y = middleY - height / 2;
          });
          break;
        }
        case 'bottom': {
          // 找到最底部的元素
          const bottomPositions: number[] = selectedElements.map((el: Element) => {
            const height: number = el.size?.height || getDefaultHeightForElement(el);
            return el.position.y + height;
          });
          const bottommost: number = Math.max(...bottomPositions);
          
          selectedElements.forEach((el: Element) => {
            const height: number = el.size?.height || getDefaultHeightForElement(el);
            el.position.y = bottommost - height;
          });
          break;
        }
      }
      
      // 创建一个元素ID映射，用于更新原始元素
      const updatedElementMap = new Map<string, Element>(
        selectedElements.map(el => [el.id, el])
      );
      
      // 更新原始元素列表
      const updatedElements: Element[] = currentPage.elements.map(el => {
        const updatedElement = updatedElementMap.get(el.id);
        return updatedElement || el;
      });
      
      // 更新页面元素
      const updatedPages: Page[] = [...pages];
      if (currentPage && currentPageIndex >= 0 && currentPageIndex < updatedPages.length) {
        updatedPages[currentPageIndex] = {
          ...currentPage,
          id: ensureId(currentPage.id),
          elements: updatedElements
        };
      }
      
      // 添加到历史记录
      const historyAction = {
        type: 'arrangement' as const,
        pageId: currentPageId,
        elements: selectedElements,
        previousElements
      };
      
      const addToHistory = get().addToHistory;
      if (addToHistory) {
        addToHistory(historyAction);
      }
      
      return { pages: updatedPages };
    }),
    
    // 分布元素
    distributeElements: (distributeType: 'horizontal' | 'vertical') => set((state: CombinedState) => {
      const { pages, currentPageId, selectedElementIds } = state;
      if (selectedElementIds.length <= 2) return state;
      
      const currentPageIndex: number = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage: Page | null = safeArrayAccess(pages, currentPageIndex, null);
      if (!currentPage) return state;
      
      const selectedElements: Element[] = ensureArray(currentPage.elements).filter(el => 
        selectedElementIds.includes(el.id)
      );
      
      if (selectedElements.length <= 2) return state;
      
      // 保存原始元素状态，用于历史记录
      const previousElements: Element[] = JSON.parse(JSON.stringify(selectedElements));
      
      // 根据分布类型计算间距
      if (distributeType === 'horizontal') {
        // 水平分布
        // 按X坐标排序
        const sortedElements: Element[] = [...selectedElements].sort((a, b) => a.position.x - b.position.x);
        
        // 找到最左和最右的元素
        if (sortedElements.length >= 2) {
          const leftElement: Element | undefined = sortedElements[0];
          const rightElement: Element | undefined = sortedElements[sortedElements.length - 1];
          
          if (leftElement && rightElement) {
            // 计算总宽度和间距
            const totalWidth: number = rightElement.position.x - leftElement.position.x;
            const spacing: number = totalWidth / (sortedElements.length - 1);
            
            // 调整中间元素的位置
            for (let i = 1; i < sortedElements.length - 1; i++) {
              const element: Element | undefined = sortedElements[i];
              if (element) {
                element.position.x = leftElement.position.x + spacing * i;
              }
            }
          }
        }
      } else {
        // 垂直分布
        // 按Y坐标排序
        const sortedElements: Element[] = [...selectedElements].sort((a, b) => a.position.y - b.position.y);
        
        // 找到最上和最下的元素
        if (sortedElements.length >= 2) {
          const topElement: Element | undefined = sortedElements[0];
          const bottomElement: Element | undefined = sortedElements[sortedElements.length - 1];
          
          if (topElement && bottomElement) {
            // 计算总高度和间距
            const totalHeight: number = bottomElement.position.y - topElement.position.y;
            const spacing: number = totalHeight / (sortedElements.length - 1);
            
            // 调整中间元素的位置
            for (let i = 1; i < sortedElements.length - 1; i++) {
              const element: Element | undefined = sortedElements[i];
              if (element) {
                element.position.y = topElement.position.y + spacing * i;
              }
            }
          }
        }
      }
      
      // 创建一个元素ID映射，用于更新原始元素
      const updatedElementMap = new Map<string, Element>(
        selectedElements.map(el => [el.id, el])
      );
      
      // 更新原始元素列表
      const updatedElements: Element[] = currentPage.elements.map(el => {
        const updatedElement = updatedElementMap.get(el.id);
        return updatedElement || el;
      });
      
      // 更新页面元素
      const updatedPages: Page[] = [...pages];
      if (currentPage && currentPageIndex >= 0 && currentPageIndex < updatedPages.length) {
        updatedPages[currentPageIndex] = {
          ...currentPage,
          id: ensureId(currentPage.id),
          elements: updatedElements
        };
      }
      
      // 添加到历史记录
      const historyAction = {
        type: 'arrangement' as const,
        pageId: currentPageId,
        elements: selectedElements,
        previousElements
      };
      
      const addToHistory = get().addToHistory;
      if (addToHistory) {
        addToHistory(historyAction);
      }
      
      return { pages: updatedPages };
    })
  };
};
