{"name": "web-typesetting-tool", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.0.19", "@chakra-ui/react": "^2.7.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@modelcontextprotocol/sdk": "^1.7.0", "@smithery/sdk": "^1.0.2", "framer-motion": "^10.12.18", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "mammoth": "^1.9.0", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.18", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "slate": "^0.94.1", "slate-react": "^0.94.2", "uuid": "^9.0.0", "zustand": "^4.3.9"}, "devDependencies": {"@types/pdfmake": "^0.2.11", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/uuid": "^9.0.2", "@vitejs/plugin-react": "^4.0.1", "typescript": "^5.1.6", "vite": "^4.4.0"}}