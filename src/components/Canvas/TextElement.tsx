import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Box, useToast } from '@chakra-ui/react';
import { createEditor, Descendant } from 'slate';
import { Slate, Editable, withReact } from 'slate-react';
import usePageStore, { Element } from '../../store/pageStore';
import { doElementsOverlap } from '../../utils/collisionDetection';

interface TextElementProps {
  element: Element;
  isSelected?: boolean;
}

// 调整大小句柄的尺寸（像素）
const HANDLE_SIZE = 12;

const TextElement: React.FC<TextElementProps> = ({ element, isSelected = false }) => {
  const [editor] = useState(() => withReact(createEditor()));
  const [isEditing, setIsEditing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [size, setSize] = useState({
    width: element.size?.width || 170,
    height: element.size?.height || 80
  });
  
  // 引用存储调整前的尺寸和鼠标位置
  const resizeStartRef = useRef({ size: { width: 0, height: 0 }, mouseX: 0, mouseY: 0 });
  // 引用元素的DOM
  const elementRef = useRef<HTMLDivElement>(null);
  
  const { updateElement, pages, currentPageId } = usePageStore();
  const toast = useToast();
  
  // 毫米转像素和像素转毫米的辅助函数
  const mmToPx = (mm: number) => mm * 3.7795275591;
  const pxToMm = (px: number) => px / 3.7795275591;
  
  // 初始化Slate值
  const initialValue: Descendant[] = [
    {
      children: [{ text: element.content }],
    } as Descendant,
  ];
  
  // 计算文本内容的适当高度
  const calculateTextHeight = useCallback((text: string) => {
    // 检查文本内容
    if (!text || text.trim() === '') {
      return 15; // 空文本使用最小高度
    }
    
    // 基础高度 - 降低为15毫米，足够单行文本显示
    const baseHeight = 15;
    // 每行估计高度（毫米） - 增加行高以适应更大的行间距
    const lineHeight = 6.5;
    // 估计文本宽度下每行能容纳的字符数 (中文字符占用更多空间)
    const charsPerLine = Math.floor(size.width / 3.5);
    
    // 计算实际行数
    let lines = 1;
    
    // 首先检查显式换行符
    const textLines = text.split('\n');
    if (textLines.length > 1) {
      // 有显式换行符，每一行再检查是否需要自动换行
      lines = textLines.reduce((count, line) => {
        // 计算这一行可能占用的行数
        const lineCount = Math.max(1, Math.ceil(line.length / Math.max(1, charsPerLine)));
        return count + lineCount;
      }, 0);
    } else {
      // 无显式换行符，只考虑自动换行
      lines = Math.max(1, Math.ceil(text.length / Math.max(1, charsPerLine)));
    }
    
    // 计算高度，单行文本使用baseHeight，多行文本添加额外高度
    const calculatedHeight = lines === 1 ? 
      baseHeight : 
      baseHeight + (lines - 1) * lineHeight;
    
    return Math.max(15, calculatedHeight); // 确保最小高度为15mm
  }, [size.width]);
  
  // 当文本内容改变时更新高度
  useEffect(() => {
    if (!isResizing && !isEditing && element.content && typeof element.content === 'string') {
      const newHeight = calculateTextHeight(element.content);
      
      // 只有当计算出的高度与当前高度差异较大时才更新
      if (Math.abs(newHeight - size.height) > 5) {
        setSize(prev => ({ ...prev, height: newHeight }));
        // 更新store中的高度
        updateElement(element.id, { 
          size: { width: size.width, height: newHeight } 
        });
      }
    }
  }, [element.content, calculateTextHeight, isResizing, isEditing, updateElement, element.id, size.width, size.height]);
  
  // 确保组件挂载时使用最新的尺寸
  useEffect(() => {
    // 计算基于内容的高度
    const calculateInitialHeight = () => {
      if (!element.content || typeof element.content !== 'string') {
        return 15; // 默认最小高度
      }
      
      // 基础高度
      const baseHeight = 15; // 更小的基础高度
      // 每行估计高度（毫米） - 增加行高以适应更大的行间距
      const lineHeight = 6.5;
      // 估计文本宽度下每行能容纳的字符数
      const width = element.size?.width || 170;
      const charsPerLine = Math.floor(width / 3.5);
      
      // 计算实际行数，考虑换行符
      let lines = 1;
      const textLines = element.content.split('\n');
      
      if (textLines.length > 1) {
        lines = textLines.reduce((count, line) => {
          const lineCount = Math.max(1, Math.ceil(line.length / Math.max(1, charsPerLine)));
          return count + lineCount;
        }, 0);
      } else {
        lines = Math.max(1, Math.ceil(element.content.length / Math.max(1, charsPerLine)));
      }
      
      // 单行使用基础高度，多行添加额外高度
      return lines === 1 ? baseHeight : baseHeight + (lines - 1) * lineHeight;
    };
    
    // 如果元素有尺寸属性，使用它的宽度，但根据内容计算高度
    if (element.size) {
      const contentBasedHeight = calculateInitialHeight();
      setSize({
        width: element.size.width,
        height: contentBasedHeight
      });
      
      // 如果当前高度与基于内容计算的高度相差较大，更新到store
      if (Math.abs(element.size.height - contentBasedHeight) > 5) {
        updateElement(element.id, { 
          size: { 
            width: element.size.width, 
            height: contentBasedHeight 
          } 
        });
      }
    } else {
      // 如果元素没有尺寸属性，设置默认宽度和基于内容的高度
      const contentBasedHeight = calculateInitialHeight();
      const defaultSize = {
        width: 170,
        height: contentBasedHeight
      };
      setSize(defaultSize);
      
      // 更新到store
      updateElement(element.id, { size: defaultSize });
    }
  }, [element.id, element.size, element.content, updateElement]);
  
  // 获取当前页面的所有元素
  const getCurrentPageElements = () => {
    const currentPage = pages.find(page => page.id === currentPageId) || pages[0];
    return currentPage.elements;
  };
  
  // 检查当前尺寸是否会导致元素重叠
  const checkOverlap = (newWidth: number, newHeight: number) => {
    // 使用新尺寸创建临时元素
    const tempElement: Element = {
      ...element,
      size: { width: newWidth, height: newHeight }
    };
    
    // 获取当前页面中除当前元素外的其他元素
    const otherElements = getCurrentPageElements().filter(el => el.id !== element.id);
    
    // 检查是否与任何其他元素重叠
    const overlaps = otherElements.some(otherElement => doElementsOverlap(tempElement, otherElement, -8));
    
    return overlaps;
  };
  
  const handleDoubleClick = () => {
    setIsEditing(true);
  };
  
  const handleChange = (value: Descendant[]) => {
    // 提取纯文本内容
    const textContent = value
      .map(node => 'children' in node ? node.children.map(child => 'text' in child ? child.text : '').join('') : '')
      .join('\n');
      
    // 计算新高度
    const newHeight = calculateTextHeight(textContent);
    
    // 更新元素内容和高度
    updateElement(element.id, { 
      content: textContent,
      size: { 
        width: size.width,
        height: newHeight
      }
    });
    
    // 更新本地状态
    setSize(prev => ({
      ...prev,
      height: newHeight
    }));
  };
  
  const handleBlur = () => {
    setIsEditing(false);
  };
  
  // 开始调整大小
  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsResizing(true);
    
    // 保存调整开始时的尺寸和鼠标位置
    resizeStartRef.current = {
      size: { ...size },
      mouseX: e.clientX,
      mouseY: e.clientY
    };
    
    // 添加全局鼠标事件监听器
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };
  
  // 调整大小过程中
  const handleResizeMove = (e: MouseEvent) => {
    if (!isResizing) return;
    
    // 阻止事件冒泡和默认行为
    e.preventDefault();
    e.stopPropagation();
    
    // 计算鼠标移动距离
    const deltaX = e.clientX - resizeStartRef.current.mouseX;
    const deltaY = e.clientY - resizeStartRef.current.mouseY;
    
    // 转换为毫米并计算新尺寸
    const deltaWidthMm = pxToMm(deltaX);
    const deltaHeightMm = pxToMm(deltaY);
    
    // 计算新尺寸（调整最小尺寸限制）
    const newWidth = Math.max(50, resizeStartRef.current.size.width + deltaWidthMm);
    const newHeight = Math.max(15, resizeStartRef.current.size.height + deltaHeightMm); // 减小最小高度限制
    
    // 直接更新DOM元素的样式，提高响应速度
    if (elementRef.current) {
      elementRef.current.style.width = `${newWidth}mm`;
      elementRef.current.style.height = `${newHeight}mm`;
    }
    
    // 使用requestAnimationFrame更新状态，减少不必要的渲染
    window.requestAnimationFrame(() => {
      setSize({
        width: newWidth,
        height: newHeight
      });
    });
  };
  
  // 强制同步元素大小到store的辅助函数
  const syncSizeToStore = useCallback(() => {
    // 只在实际尺寸发生变化时更新，并且确保仅更新必要的属性
    if (element.size?.width !== size.width || element.size?.height !== size.height) {
      const roundedSize = {
        width: Math.round(size.width),
        height: Math.round(size.height)
      };
      updateElement(element.id, { size: roundedSize });
    }
  }, [element.id, element.size, size.width, size.height, updateElement]);
  
  // 在组件卸载前同步一次大小到store
  useEffect(() => {
    // 在组件卸载时清理
    return () => {
      // 清理事件监听器
      if (isResizing) {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      }
      
      // 最后一次同步尺寸到store
      if (!element.size || 
          element.size.width !== Math.round(size.width) || 
          element.size.height !== Math.round(size.height)) {
        syncSizeToStore();
      }
    };
  }, [isResizing, syncSizeToStore, element.size, size]);
  
  // 结束调整大小
  const handleResizeEnd = () => {
    if (!isResizing) return;
    
    // 先移除事件监听器
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
    
    // 检查最终尺寸是否会导致元素重叠
    const finalSize = {
      width: Math.round(size.width),
      height: Math.round(size.height)
    };
    
    // 检测是否与其他元素重叠
    const hasOverlap = checkOverlap(finalSize.width, finalSize.height);
    
    if (hasOverlap) {
      // 如果会导致重叠，回退到原始尺寸并提示用户
      setSize(resizeStartRef.current.size);
      
      // 恢复DOM元素的原始样式
      if (elementRef.current) {
        elementRef.current.style.width = `${resizeStartRef.current.size.width}mm`;
        elementRef.current.style.height = `${resizeStartRef.current.size.height}mm`;
      }
      
      toast({
        title: "调整大小失败",
        description: "调整后的大小会导致与其他元素重叠",
        status: "warning",
        duration: 3000,
        isClosable: true,
      });
      
      // 设置状态
      setIsResizing(false);
    } else {
      // 延迟设置状态，避免在可能的渲染过程中状态更新
      setTimeout(() => {
        // 先更新store，确保持久化
        updateElement(element.id, { size: finalSize });
        // 然后更新状态
        setIsResizing(false);
      }, 0);
    }
  };
  
  // 根据字号属性返回相应的字体大小
  const getFontSize = () => {
    switch (element.fontSize) {
      case 'small':
        return '14px';
      case 'large':
        return '24px';
      case 'medium':
      default:
        return '16px'; // 调整默认字体大小
    }
  };
  
  return (
    <Box 
      ref={elementRef}
      position="relative"
      width={`${size.width}mm`}
      height={`${size.height}mm`}
      minW="50px" 
      minH="15px" // 减小最小高度限制
      p={1} // 减小内边距
      border={isEditing 
        ? "1px dashed blue" 
        : isResizing 
          ? "1px solid blue" 
          : isSelected 
            ? "1px solid blue" 
            : "1px solid transparent"}
      borderRadius="md"
      onDoubleClick={handleDoubleClick}
      fontSize={getFontSize()}
      bg="white"
      overflow="hidden" // 超出文本隐藏而不是溢出
      transition="border-color 0.2s ease"
      _hover={{ 
        borderColor: isSelected || isEditing || isResizing ? "blue" : "gray.300" 
      }}
      lineHeight="1.5" // 增加行高
    >
      {isEditing ? (
        <Slate editor={editor} value={initialValue} onChange={handleChange}>
          <Editable 
            autoFocus 
            onBlur={handleBlur}
            onKeyDown={(e) => {
              // 阻止退格键事件冒泡，确保退格键能正常删除文本
              if (e.key === 'Backspace') {
                e.stopPropagation();
              }
            }}
            style={{ 
              fontSize: getFontSize(),
              height: '100%',
              width: '100%', 
              overflow: 'hidden',
              lineHeight: '1.5', // 增加行高
              padding: '0',
              margin: '0'
            }}
          />
        </Slate>
      ) : (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          overflow: 'hidden', 
          lineHeight: '1.5', // 增加行高
          whiteSpace: 'pre-wrap', // 保持换行和空格
          wordBreak: 'break-word' // 确保长单词能够换行
        }}>
          {element.content}
        </div>
      )}
      
      {(isSelected || isResizing) && (
        <Box
          position="absolute"
          right={`-${HANDLE_SIZE / 2}px`}
          bottom={`-${HANDLE_SIZE / 2}px`}
          width={`${HANDLE_SIZE}px`}
          height={`${HANDLE_SIZE}px`}
          bg="blue.500"
          borderRadius="full"
          cursor="nwse-resize"
          onMouseDown={handleResizeStart}
          opacity={0.7}
          _hover={{ opacity: 1 }}
          zIndex={10}
        />
      )}
    </Box>
  );
};

export default TextElement;