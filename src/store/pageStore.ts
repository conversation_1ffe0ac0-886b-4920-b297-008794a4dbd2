import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { nanoid } from 'nanoid';
import { Element, Page, HistoryAction } from './types';
import { 
  safeArrayAccess, 
  getCurrentPage, 
  getCurrentPageIndex, 
  findPageById, 
  findPageIndexById,
  getDefaultWidthForElement,
  getDefaultHeightForElement
} from './utils/storeUtils';

// 定义状态接口
interface PageState {
  // 状态数据
  pages: Page[];
  currentPageId: string;
  selectedElementIds: string[];
  clipboard: Element[] | null;
  history: HistoryAction[];
  historyIndex: number;
  questionsPerPage: number;
  documentVersion: 'teacher' | 'student';
  
  // 方法
  arrangeElementsWaterfall: (specifiedColumns?: number) => void;  // 瀑布流排列函数
  
  // 元素管理
  addElement: (element: Omit<Element, 'id'>) => string;
  updateElement: (id: string, updates: Partial<Element>) => void;
  deleteElements: (ids: string[]) => void;
  
  // 页面管理
  addPage: () => string;
  deletePage: (id: string) => void;
  setCurrentPage: (id: string) => void;
  
  // 页面设置
  updatePageHeader: (header: string) => void;
  updatePageFooter: (footer: string) => void;
  updateHeaderColor: (color: string) => void; // 新增：更新页眉颜色
  updateFooterColor: (color: string) => void; // 新增：更新页脚颜色
  setQuestionsPerPage: (count: number) => void; // 设置每页题目数量
  setDocumentVersion: (version: 'teacher' | 'student') => void; // 设置文档版本
  
  // 选择和编辑
  selectElement: (id: string, isMultiSelect?: boolean) => void;
  selectMultipleElements: (ids: string[]) => void;
  clearSelection: () => void;
  deleteSelectedElements: () => void;
  
  // 复制粘贴
  copySelectedElements: () => void;
  cutSelectedElements: () => void;
  pasteElements: (position?: { x: number, y: number }) => void;
  
  // 元素对齐
  alignElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => void;
  distributeElements: (distributeType: 'horizontal' | 'vertical') => void;
  
  // 撤销重做
  undo: () => void;
  redo: () => void;
  addToHistory: (action: HistoryAction) => void;
}

// 创建状态管理
const usePageStore = create<PageState>((set) => {
  // 创建初始页面
  const initialPage: Page = {
    id: uuidv4(),
    elements: [],
    header: '页面 {{pageNumber}}',
    footer: '{{pageNumber}}',  // 修改为只显示页码
    headerColor: 'orange.300', // 更改默认页眉颜色为饱和度更高的橙色
    footerColor: 'orange.300'  // 更改默认页脚颜色为饱和度更高的橙色
  };

  return {
    pages: [initialPage],
    currentPageId: initialPage.id,
    selectedElementIds: [],
    clipboard: null,
    history: [],
    historyIndex: -1,
    questionsPerPage: 0, // 默认不指定每页题目数量
    documentVersion: 'teacher', // 默认为教师版
    
    // 添加历史记录
    addToHistory: (action) => set((state) => {
      // 如果当前不在历史记录的最后位置，需要清除之后的历史记录
      const newHistory = state.historyIndex < state.history.length - 1
        ? state.history.slice(0, state.historyIndex + 1)
        : [...state.history];
      
      return {
        history: [...newHistory, action],
        historyIndex: newHistory.length,
      };
    }),
    
    // 撤销
    undo: () => set((state) => {
      if (state.historyIndex < 0) return state;
      
      const action = state.history[state.historyIndex];
      const pageIndex = findPageIndexById(state.pages, action.pageId || '');
      
      if (pageIndex === -1) return state;
      
      // 根据操作类型进行撤销
      const updatedPages = [...state.pages];
      
      if (!action) {
        return state;
      }
      
      switch (action.type) {
        case 'add':
          // 撤销添加 - 从页面移除元素
          const elementIdsToRemove = action.elements.map(el => el.id);
          updatedPages[pageIndex] = {
            ...updatedPages[pageIndex],
            elements: updatedPages[pageIndex].elements.filter(
              el => !elementIdsToRemove.includes(el.id)
            )
          };
          break;
          
        case 'delete':
          // 撤销删除 - 恢复删除的元素
          updatedPages[pageIndex] = {
            ...updatedPages[pageIndex],
            elements: [...updatedPages[pageIndex].elements, ...action.elements]
          };
          break;
          
        case 'update':
        case 'arrangement':
          // 撤销更新或排列 - 恢复到之前的元素状态
          if (action.previousElements) {
            updatedPages[pageIndex] = {
              ...updatedPages[pageIndex],
              elements: updatedPages[pageIndex].elements.map(el => {
                const prevElement = action.previousElements?.find(prev => prev.id === el.id);
                return prevElement || el;
              })
            };
          }
          break;
      }
      
      return {
        pages: updatedPages,
        historyIndex: state.historyIndex - 1
      };
    }),
    
    // 重做
    redo: () => set((state) => {
      if (state.historyIndex >= state.history.length - 1) return state;
      
      const action = state.history[state.historyIndex + 1];
      const pageIndex = findPageIndexById(state.pages, action.pageId || '');
      
      if (pageIndex === -1) return state;
      
      // 根据操作类型进行重做
      const updatedPages = [...state.pages];
      
      if (!action) {
        return state;
      }
      
      switch (action.type) {
        case 'add':
          // 重做添加 - 添加元素
          updatedPages[pageIndex] = {
            ...updatedPages[pageIndex],
            elements: [...updatedPages[pageIndex].elements, ...action.elements]
          };
          break;
          
        case 'delete':
          // 重做删除 - 再次删除元素
          const elementIdsToRemove = action.elements.map(el => el.id);
          updatedPages[pageIndex] = {
            ...updatedPages[pageIndex],
            elements: updatedPages[pageIndex].elements.filter(
              el => !elementIdsToRemove.includes(el.id)
            )
          };
          break;
          
        case 'update':
        case 'arrangement':
          // 重做更新或排列 - 应用更新后的元素状态
          updatedPages[pageIndex] = {
            ...updatedPages[pageIndex],
            elements: action.elements
          };
          break;
      }
      
      return {
        pages: updatedPages,
        historyIndex: state.historyIndex + 1
      };
    }),
    
    // 页面管理
    addPage: () => set((state) => {
      const newPage: Page = {
        id: uuidv4(),
        elements: [],
        header: state.pages[0].header,
        footer: state.pages[0].footer
      };
      
      return { 
        pages: [...state.pages, newPage],
        currentPageId: newPage.id
      };
    }),
    
    deletePage: (id) => set((state) => {
      if (state.pages.length <= 1) return state;
      
      const newPages = state.pages.filter(page => page.id !== id);
      const newCurrentId = state.currentPageId === id 
        ? newPages[0].id 
        : state.currentPageId;
        
      return { 
        pages: newPages,
        currentPageId: newCurrentId
      };
    }),
    
    setCurrentPage: (id) => set({ currentPageId: id }),
    
    // 元素选择管理
    selectElement: (id, isMultiSelect = false) => set((state) => {
      if (isMultiSelect) {
        // 多选模式 - 切换选中状态
        return {
          selectedElementIds: state.selectedElementIds.includes(id)
            ? state.selectedElementIds.filter(elemId => elemId !== id)
            : [...state.selectedElementIds, id]
        };
      } else {
        // 单选模式 - 替换当前选择
        return { selectedElementIds: [id] };
      }
    }),
    
    selectMultipleElements: (ids) => set(() => ({
      selectedElementIds: ids
    })),
    
    clearSelection: () => set(() => ({
      selectedElementIds: []
    })),
    
    // 在addElement函数中修改标题组件的固定位置
    addElement: (element) => {
      // 调试输出
      console.log("正在添加元素:", {
        type: element.type,
        contentType: typeof element.content,
        hasSize: !!element.size
      });
      
      const newElement: Element = {
        ...element,
        id: nanoid(),
      };
      
      // 确保对于图片类型保留原始内容和尺寸
      if (element.type === 'image') {
        console.log("处理图片元素:", {
          id: newElement.id,
          contentLength: typeof element.content === 'string' ? element.content.length : 0,
          hasSize: !!element.size
        });
      }
      
      set(state => {
        const currentPageIndex = state.pages.findIndex(page => page.id === state.currentPageId);
        if (currentPageIndex === -1) return state;
        
        const updatedPages = [...state.pages];
        const newElements = [...updatedPages[currentPageIndex].elements, newElement];
        
        updatedPages[currentPageIndex] = {
          ...updatedPages[currentPageIndex],
          elements: newElements
        };
        
        // 添加到历史记录
        const historyAction: HistoryAction = {
          type: 'add',
          pageId: state.currentPageId,
          elements: [newElement]
        };
        
        return { 
          pages: updatedPages, 
          selectedElementIds: [newElement.id],
          history: [...state.history, historyAction],
          historyIndex: state.history.length
        };
      });
    },
    
    updateElement: (id, updates) => set((state) => {
      const currentPageIndex = state.pages.findIndex(page => page.id === state.currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = state.pages[currentPageIndex];
      const elementIndex = currentPage.elements.findIndex(el => el.id === id);
      
      if (elementIndex === -1) return state;
      
      // 保存更新前的元素，用于历史记录
      const previousElement = currentPage.elements[elementIndex];
      
      const updatedPages = [...state.pages];
      const updatedElements = [...currentPage.elements];
      
      updatedElements[elementIndex] = { ...previousElement, ...updates };
      
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: updatedElements
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'update',
        pageId: state.currentPageId,
        elements: [updatedElements[elementIndex]],
        previousElements: [previousElement]
      };
      
      return { 
        pages: updatedPages,
        history: [...state.history, historyAction],
        historyIndex: state.history.length 
      };
    }),
    
    deleteElement: (id) => set((state) => {
      const currentPageIndex = state.pages.findIndex(page => page.id === state.currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = state.pages[currentPageIndex];
      const elementToDelete = currentPage.elements.find(el => el.id === id);
      
      if (!elementToDelete) return state;
      
      const updatedPages = [...state.pages];
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: currentPage.elements.filter(element => element.id !== id)
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'delete',
        pageId: state.currentPageId,
        elements: [elementToDelete]
      };
      
      return { 
        pages: updatedPages,
        selectedElementIds: state.selectedElementIds.filter(elemId => elemId !== id),
        history: [...state.history, historyAction],
        historyIndex: state.history.length
      };
    }),
    
    // 删除选中的元素
    deleteSelectedElements: () => set((state) => {
      const { currentPageId, selectedElementIds, pages } = state;
      if (selectedElementIds.length === 0) return state;
      
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = pages[currentPageIndex];
      const elementsToDelete = currentPage.elements.filter(
        el => selectedElementIds.includes(el.id)
      );
      
      // 如果没有找到任何要删除的元素，直接返回
      if (elementsToDelete.length === 0) return state;
      
      const updatedPages = [...pages];
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: currentPage.elements.filter(
          element => !selectedElementIds.includes(element.id)
        )
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'delete',
        pageId: currentPageId,
        elements: elementsToDelete
      };
      
      return {
        pages: updatedPages,
        selectedElementIds: [],
        history: [...state.history, historyAction],
        historyIndex: state.history.length
      };
    }),
    
    // 复制选中的元素
    copySelectedElements: () => set((state) => {
      const { currentPageId, selectedElementIds, pages } = state;
      if (selectedElementIds.length === 0) return state;
      
      const currentPage = pages.find(page => page.id === currentPageId);
      if (!currentPage) return state;
      
      const elementsToCopy = currentPage.elements.filter(
        el => selectedElementIds.includes(el.id)
      );
      
      return { clipboard: elementsToCopy };
    }),
    
    // 剪切选中的元素
    cutSelectedElements: () => set((state) => {
      // 先复制当前选中的元素到剪贴板
      const { selectedElementIds, currentPageId, pages } = state;
      if (selectedElementIds.length === 0) return state;
      
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = pages[currentPageIndex];
      const elementsToCopy = currentPage.elements.filter(
        el => selectedElementIds.includes(el.id)
      );
      
      // 然后删除选中的元素
      const updatedPages = [...pages];
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: currentPage.elements.filter(
          element => !selectedElementIds.includes(element.id)
        )
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'delete',
        pageId: currentPageId,
        elements: elementsToCopy
      };
      
      return {
        clipboard: elementsToCopy,
        pages: updatedPages,
        selectedElementIds: [],
        history: [...state.history, historyAction],
        historyIndex: state.history.length
      };
    }),
    
    // 粘贴元素
    pasteElements: (position) => set((state) => {
      const { clipboard, currentPageId, pages } = state;
      if (!clipboard || clipboard.length === 0) return state;
      
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      // 创建新元素，保持相对位置关系
      const newElements: Element[] = [];
      const newElementIds: string[] = [];
      
      // 如果提供了粘贴位置，计算位置偏移量
      let offsetX = 0;
      let offsetY = 0;
      
      if (position) {
        // 找到剪贴板中最左上角的元素
        const topLeftElement = clipboard.reduce((prev, curr) => {
          if (curr.position.y < prev.position.y) return curr;
          if (curr.position.y === prev.position.y && curr.position.x < prev.position.x) return curr;
          return prev;
        }, clipboard[0]);
        
        // 计算偏移量
        offsetX = position.x - topLeftElement.position.x;
        offsetY = position.y - topLeftElement.position.y;
      } else {
        // 默认偏移量，向右下方偏移一点
        offsetX = 10;
        offsetY = 10;
      }
      
      // 创建新元素
      clipboard.forEach(element => {
        const newElement: Element = {
          ...element,
          id: nanoid(),
          position: {
            x: element.position.x + offsetX,
            y: element.position.y + offsetY
          }
        };
        
        newElements.push(newElement);
        newElementIds.push(newElement.id);
      });
      
      // 更新页面
      const updatedPages = [...pages];
      updatedPages[currentPageIndex] = {
        ...updatedPages[currentPageIndex],
        elements: [...updatedPages[currentPageIndex].elements, ...newElements]
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'add',
        pageId: currentPageId,
        elements: newElements
      };
      
      return {
        pages: updatedPages,
        selectedElementIds: newElementIds,
        history: [...state.history, historyAction],
        historyIndex: state.history.length
      };
    }),
    
    // 添加瀑布流排列功能
    arrangeElementsWaterfall: (specifiedColumns?: number) => set((state) => {
      const { pages, currentPageId } = state;
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = pages[currentPageIndex];
      let elements = [...currentPage.elements];
      let pagesCreated = 0;
      
      // 定义布局常量
      const MARGIN_TOP = 35; // 顶部边距（mm）
      const MARGIN_LEFT = 20; // 左侧边距（mm）
      const PAGE_WIDTH = 210; // A4纸宽度（mm）
      const PAGE_HEIGHT = 297; // A4纸高度（mm）
      const PAGE_MARGIN_BOTTOM = 20; // 底部边距（mm）
      const ELEMENT_SPACING = state.questionsPerPage === 0 ? 1 : 2; // 元素间距（mm）- 无题目限制时使用更小间距
      const DEFAULT_TEXT_HEIGHT = 15; // 文本元素默认高度（mm）
      const NUMBER_ANSWER_SPACE = 10; // 答题区域高度（mm）- 对所有选项使用相同的值
      const TITLE_HEIGHT = 20; // 标题高度（mm）

      // 计算文本高度的辅助函数
      const calculateTextHeight = (element: Element): number => {
        if (element.size?.height) {
          return element.size.height;
        }
        
        const textContent = typeof element.content === 'string' ? element.content : '';
        const hasLineBreaks = textContent.includes('\n');
        const elementWidth = element.size?.width || 170;
        const charsPerLine = Math.floor(elementWidth / 3.5);
        
        if (!hasLineBreaks && textContent.length <= charsPerLine) {
          return DEFAULT_TEXT_HEIGHT;
        } else {
          const lines = hasLineBreaks ? 
            textContent.split('\n').reduce((count, line) => {
              return count + Math.max(1, Math.ceil(line.length / charsPerLine));
            }, 0) : 
            Math.max(1, Math.ceil(textContent.length / charsPerLine));
          
          return Math.max(DEFAULT_TEXT_HEIGHT, DEFAULT_TEXT_HEIGHT + (lines - 1) * 6.5);
        }
      };
      
      // 创建页面元素数组
      const pageElements: { [key: number]: Element[] } = {
        [currentPageIndex]: []
      };

      if (state.questionsPerPage === 0) {
        // 先按Y坐标排序，保持元素的相对位置
        elements.sort((a, b) => {
          // 标题始终在最前
          if (a.type === 'title') return -1;
          if (b.type === 'title') return 1;
          
          // 小标题排在标题之后，但在其他元素之前
          if (a.type === 'subtitle' && b.type !== 'subtitle') return -1;
          if (b.type === 'subtitle' && a.type !== 'subtitle') return 1;
          
          // 其他元素按当前Y坐标排序
          return a.position.y - b.position.y;
        });

        // 分开处理标题和小标题，确保它们之间有合适的间距
        const titleElements = elements.filter(el => el.type === 'title');
        const subtitleElements = elements.filter(el => el.type === 'subtitle');
        const contentElements = elements.filter(el => el.type !== 'title' && el.type !== 'subtitle');
        
        let currentY = MARGIN_TOP;
        let currentPageIdx = currentPageIndex;

        // 处理标题元素
        titleElements.forEach(element => {
          pageElements[currentPageIdx].push({
            ...element,
            position: {
              x: PAGE_WIDTH / 2, // 标题居中
              y: currentY
            }
          });
          currentY += TITLE_HEIGHT + ELEMENT_SPACING;
        });
        
        // 处理小标题元素
        subtitleElements.forEach(element => {
          pageElements[currentPageIdx].push({
            ...element,
            position: {
              x: MARGIN_LEFT,
              y: currentY
            }
          });
          currentY += DEFAULT_TEXT_HEIGHT + 5 + ELEMENT_SPACING;
        });

        // 处理其他内容元素
        for (let i = 0; i < contentElements.length; i++) {
          const element = contentElements[i];
          // 计算元素高度
          let elementHeight;
          let elementSize = element.size ? { 
            width: element.size.width, 
            height: element.size.height 
          } : undefined;
          
          if (element.type === 'image' && element.size) {
            elementHeight = element.size.height;
          } else if (element.type === 'number') {
            elementHeight = Math.min(30, 15 + Math.floor(element.content.length / 200) * 5);
          } else if (element.type === 'text') {
            elementHeight = calculateTextHeight(element);
          } else if (element.type === 'question') {
            elementHeight = Math.max(DEFAULT_TEXT_HEIGHT, Math.min(35, DEFAULT_TEXT_HEIGHT + Math.floor(element.content.length / 100) * 5));
          } else {
            elementHeight = DEFAULT_TEXT_HEIGHT;
          }

          // 检查是否需要调整元素大小或行间距
          if (currentY + elementHeight > PAGE_HEIGHT - PAGE_MARGIN_BOTTOM) {
            // 适应策略1：如果是图片元素，尝试缩小图片
            if (element.type === 'image' && element.size) {
              // 计算可用高度
              const availableHeight = PAGE_HEIGHT - PAGE_MARGIN_BOTTOM - currentY;
              
              // 如果可用高度不是太小，则缩小图片
              if (availableHeight >= 30) { // 最小保留30mm的图片高度
                // 计算缩放比例
                const scaleRatio = availableHeight / element.size.height;
                // 更新图片尺寸
                elementHeight = availableHeight;
                elementSize = {
                  width: Math.round(element.size.width * scaleRatio),
                  height: Math.round(availableHeight)
                };
              } else {
                // 可用空间太小，创建新页面
                currentPageIdx = currentPageIndex + ++pagesCreated;
                if (!pageElements[currentPageIdx]) {
                  pageElements[currentPageIdx] = [];
                }
                currentY = MARGIN_TOP;
              }
            } 
            // 适应策略2：如果剩余空间不足且不是图片，创建新页面
            else {
              currentPageIdx = currentPageIndex + ++pagesCreated;
              if (!pageElements[currentPageIdx]) {
                pageElements[currentPageIdx] = [];
              }
              currentY = MARGIN_TOP;
            }
          }

          // 确定X坐标
          let xPosition = MARGIN_LEFT;

          // 添加元素到当前页面，应用调整后的尺寸
          pageElements[currentPageIdx].push({
            ...element,
            position: {
              x: xPosition,
              y: currentY
            },
            // 如果是图片且尺寸有调整，使用新尺寸
            ...(element.type === 'image' && elementSize ? { size: elementSize } : {})
          });

          // 更新Y坐标，使用调整后的元素高度
          if (element.type === 'number') {
            // 检查下一个元素的类型，决定是否添加答题区域空间
            const nextElement = (i < contentElements.length - 1) ? contentElements[i + 1] : null;
            const isNextElementImage = nextElement && nextElement.type === 'image';
            
            // 如果是最后一个元素或下一个元素是图片，不添加答题区域
            if (!nextElement || isNextElementImage) {
              currentY += elementHeight + ELEMENT_SPACING;
            } else {
              // 对于题目序号，使用较小的答题区域空间
              currentY += elementHeight + ELEMENT_SPACING + NUMBER_ANSWER_SPACE;
            }
          } else {
            // 减小元素间距以节省空间
            currentY += elementHeight + (ELEMENT_SPACING * 0.7); // 减少30%的元素间距
          }
        }
      } else {
        // 计算文本元素高度的辅助函数
      function calculateGroupHeight(group: Element[]): { totalHeight: number, hasImage: boolean } {
        let groupTotalHeight = 0;
        let hasImage = false;
        
        // 计算每个元素的高度并累加得到组的总高度
        group.forEach((element, index) => {
          if (element.type === 'image') {
            hasImage = true;
          }
          
          let elementHeight;
          if (element.type === 'subtitle') {
            elementHeight = DEFAULT_TEXT_HEIGHT + 5;
          } else if (element.type === 'image' && element.size) {
            elementHeight = element.size.height;
          } else if (element.type === 'number') {
            const contentLength = typeof element.content === 'string' 
              ? element.content.length 
              : 3;
            elementHeight = Math.max(15, Math.min(30, 15 + Math.floor(contentLength / 100) * 5));
            
            // 如果序号后面没有图片，加上答题区高度
            if (!hasImage && index === 0 && group.length > 1) {
              elementHeight += NUMBER_ANSWER_SPACE;
            }
          } else if (element.type === 'text') {
            elementHeight = calculateTextHeight(element);
          } else if (element.type === 'question') {
            const contentLength = typeof element.content === 'object' 
              ? JSON.stringify(element.content).length 
              : 20;
            elementHeight = Math.max(DEFAULT_TEXT_HEIGHT, Math.min(35, DEFAULT_TEXT_HEIGHT + Math.floor(contentLength / 100) * 5));
          } else {
            elementHeight = DEFAULT_TEXT_HEIGHT;
          }
          
          // 每个元素之间都有间距
          groupTotalHeight += elementHeight + ELEMENT_SPACING;
        });
        
        return { totalHeight: groupTotalHeight, hasImage };
      }
      
      // 首先处理标题元素，放在页面顶部居中
      const titleElements = elements.filter(el => el.type === 'title');
      const subtitleElements = elements.filter(el => el.type === 'subtitle');
      
      // 获取标题和小标题的总高度
      let titleTotalHeight = 0;
      if (titleElements.length > 0) {
        titleTotalHeight += TITLE_HEIGHT;
      }
      
      // 处理标题元素，放在页面顶部居中
      titleElements.forEach(element => {
        pageElements[currentPageIndex].push({
          ...element,
          position: {
            x: PAGE_WIDTH / 2, // 标题居中
            y: MARGIN_TOP  // 在保护区域下方
          }
        });
      });
      
      // 处理小标题元素，放在标题下方
      let subtitleY = MARGIN_TOP + (titleElements.length > 0 ? TITLE_HEIGHT + ELEMENT_SPACING : 0);
      subtitleElements.forEach(element => {
        pageElements[currentPageIndex].push({
          ...element,
          position: {
            x: MARGIN_LEFT,
            y: subtitleY
          }
        });
        subtitleY += DEFAULT_TEXT_HEIGHT + 5 + ELEMENT_SPACING;
        titleTotalHeight += DEFAULT_TEXT_HEIGHT + 5 + ELEMENT_SPACING;
      });
      
      // 处理非标题和非小标题元素
      const contentElements = elements.filter(el => el.type !== 'title' && el.type !== 'subtitle');
      
        // 创建题目组，每个题目组包含一个序号元素和其后的内容
        const questionGroups: Element[][] = [];
        let currentGroup: Element[] = [];
        let currentNumberElement: Element | null = null;
        
        // 将元素分组到各个题目中
        contentElements.forEach(element => {
          if (element.type === 'number') {
            // 如果已有组，则保存
            if (currentGroup.length > 0) {
              questionGroups.push(currentGroup);
            }
            // 开始新的组，保存序号元素引用
            currentGroup = [element];
            currentNumberElement = element;
          } else {
            // 如果还没有序号元素，则创建一个默认组
            if (currentGroup.length === 0 && !currentNumberElement) {
              // 开始一个没有序号的组
              currentGroup = [];
            }
            // 添加到当前组
            currentGroup.push(element);
          }
        });
        
        // 添加最后一个组
        if (currentGroup.length > 0) {
          questionGroups.push(currentGroup);
        }

        // 如果没有找到任何题目组，则将所有内容元素视为一个组
        if (questionGroups.length === 0 && contentElements.length > 0) {
          questionGroups.push(contentElements);
        }
        
        // 按题目与内容的关系重排内容元素
        elements.sort((a, b) => {
          // 标题始终在最前
          if (a.type === 'title') return -1;
          if (b.type === 'title') return 1;
          
          // 小标题排在标题之后，但在其他元素之前
          if (a.type === 'subtitle' && b.type !== 'subtitle') return -1;
          if (b.type === 'subtitle' && a.type !== 'subtitle') return 1;
          
          // 其他元素按Y坐标排序
          return a.position.y - b.position.y;
        });
        
        // 根据每页题目数决定分页
        const { questionsPerPage } = state;
        const groupsPerPage = questionsPerPage === 0 ? Number.MAX_SAFE_INTEGER : Math.max(1, questionsPerPage);
        
        // 开始放置题目
        let currentY = MARGIN_TOP + titleTotalHeight + (titleTotalHeight > 0 ? ELEMENT_SPACING : 0);
        let currentPageIdx = currentPageIndex;
        let groupsInCurrentPage = 0;
        let numbersInCurrentPage = 0; // 跟踪当前页面的序号数量
        
        // 需要创建新页面的情况：
        // 1. 已经放置了足够数量的题目（groupsInCurrentPage >= groupsPerPage）
        // 2. 当前页剩余空间不足以容纳整个题目
        // 3. 当 questionsPerPage 为 1 时，当前页已有序号元素
        
        questionGroups.forEach(group => {
          // 检查这个题目组是否能完整地放入当前页面
          const { totalHeight: groupTotalHeight, hasImage } = calculateGroupHeight(group);
          
          // 题目后增加的额外间距
          // 对于"1题/页"和"2题/页"使用相同的组间距，只有"无"使用更小的间距
          const groupSpacing = questionsPerPage === 0 ? 2 : 5;
          
          // 检查组中是否包含序号元素
          const hasNumberElement = group.some(element => element.type === 'number');
          
          // 检查是否需要开始新页面
          const pageRemainingSpace = PAGE_HEIGHT - PAGE_MARGIN_BOTTOM - currentY;
          const needNewPage = (currentPageIdx !== currentPageIndex && groupsInCurrentPage >= groupsPerPage) || 
                            (groupTotalHeight + groupSpacing) > pageRemainingSpace ||
                            (questionsPerPage === 1 && hasNumberElement && numbersInCurrentPage >= 1);
          
          if (needNewPage) {
            // 检查是否有下一页可用
            const nextPageExists = currentPageIdx + 1 < state.pages.length;
            
            if (nextPageExists) {
              // 使用已有的下一页，并保留其原有内容
              currentPageIdx++;
              
              // 如果下一页还没有在 pageElements 中初始化，则用原页面的元素初始化
              if (!pageElements[currentPageIdx]) {
                const nextPage = state.pages[currentPageIdx];
                pageElements[currentPageIdx] = [...nextPage.elements];
              }

              // 重置Y坐标到页面顶部，为新的序号元素腾出空间
              currentY = MARGIN_TOP;

              // 如果下一页有内容，需要将所有内容向下移动
              if (pageElements[currentPageIdx].length > 0) {
                // 计算新序号组的总高度
                const { totalHeight: newGroupHeight } = calculateGroupHeight(group);
                
                // 将现有元素向下移动
                pageElements[currentPageIdx] = pageElements[currentPageIdx].map(element => ({
                  ...element,
                  position: {
                    ...element.position,
                    y: element.position.y + newGroupHeight + ELEMENT_SPACING
                  }
                }));
              }
            } else {
              // 如果没有下一页，则创建新页面
            currentPageIdx = currentPageIndex + ++pagesCreated;
            if (!pageElements[currentPageIdx]) {
              pageElements[currentPageIdx] = [];
            }
            currentY = MARGIN_TOP;
            }
            
            groupsInCurrentPage = 0;
            numbersInCurrentPage = 0; // 重置序号计数
          }
          
          // 处理当前组中的所有元素
          let hasProcessedImage = false;
          let lastNumberIndex = -1;
          
          group.forEach((element, index) => {
            if (element.type === 'number') {
              numbersInCurrentPage++; // 增加序号计数
              lastNumberIndex = index;
            }
            
            if (element.type === 'image') {
              hasProcessedImage = true;
            }
            
            // 计算元素高度
            let elementHeight;
            if (element.type === 'subtitle') {
              elementHeight = DEFAULT_TEXT_HEIGHT + 5;
            } else if (element.type === 'image' && element.size) {
              elementHeight = element.size.height;
            } else if (element.type === 'number') {
              const contentLength = typeof element.content === 'string' 
                ? element.content.length 
                : 3;
              elementHeight = Math.max(15, Math.min(30, 15 + Math.floor(contentLength / 100) * 5));
            } else if (element.type === 'text') {
              elementHeight = calculateTextHeight(element);
            } else if (element.type === 'question') {
              const contentLength = typeof element.content === 'object' 
                ? JSON.stringify(element.content).length 
                : 20;
              elementHeight = Math.max(DEFAULT_TEXT_HEIGHT, Math.min(35, DEFAULT_TEXT_HEIGHT + Math.floor(contentLength / 100) * 5));
            } else {
              elementHeight = DEFAULT_TEXT_HEIGHT;
            }
            
            // 确保当前页面数组已创建
            if (!pageElements[currentPageIdx]) {
              pageElements[currentPageIdx] = [];
            }
            
            // 添加元素到当前页面
            pageElements[currentPageIdx].push({
              ...element,
              position: {
                x: element.type === 'title' ? PAGE_WIDTH / 2 : MARGIN_LEFT,
                y: currentY
              }
            });
            
            // 更新Y坐标
            if (element.type === 'number') {
              // 检查下一个元素是否为图片
              const nextElement = group[index + 1];
              const isNextElementImage = nextElement && nextElement.type === 'image';
              
              if (!isNextElementImage) {
                // 为序号元素增加答题区空间
                currentY += elementHeight + ELEMENT_SPACING + NUMBER_ANSWER_SPACE;
              } else {
                // 如果后面是图片，只增加正常间距
                currentY += elementHeight + ELEMENT_SPACING;
              }
            } else {
              // 普通元素使用标准间距
              currentY += elementHeight + ELEMENT_SPACING;
            }
          });
          
          // 确认本组是否最后处理了序号元素
          const endsWithNumberElement = lastNumberIndex === group.length - 1;
          
          // 增加组间距，如果组的最后一个元素是序号元素，则不增加额外间距
          if (!endsWithNumberElement) {
            currentY += groupSpacing; // 题目之间额外增加一点间距
          }
          
          groupsInCurrentPage++;
        });
      }
      
      // 自动创建所需的新页面
      const updatedPages = [...state.pages];
      while (updatedPages.length <= currentPageIndex + pagesCreated) {
        updatedPages.push({
          id: uuidv4(),
          elements: [],
          header: updatedPages[0].header,
          footer: updatedPages[0].footer
        });
      }

      // 更新所有页面的元素
      Object.entries(pageElements).forEach(([pageIdx, elements]) => {
        updatedPages[parseInt(pageIdx)].elements = elements;
      });
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'arrangement',
        pageId: currentPageId,
        elements: pageElements[currentPageIndex],
        previousElements: currentPage.elements
      };

      state.addToHistory(historyAction);

      return { ...state, pages: updatedPages };
    }),
    
    updatePageHeader: (header) => set((state) => {
      // 检查header是否是有效的字符串，并过滤掉可能的UUID
      let safeHeader = '页面 {{pageNumber}}';
      
      if (typeof header === 'string' && header.trim() !== '') {
        // 检查是否是UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(header)) {
          safeHeader = header;
        }
      }
      
      const updatedPages = state.pages.map(page => {
        if (page.id === state.currentPageId) {
          return { ...page, header: safeHeader };
        }
        return page;
      });
      
      return { ...state, pages: updatedPages };
    }),
    
    updatePageFooter: (footer) => set((state) => {
      // 检查footer是否是有效的字符串，并过滤掉可能的UUID
      let safeFooter = '{{pageNumber}}';  // 修改默认值为只显示页码
      
      if (typeof footer === 'string' && footer.trim() !== '') {
        // 检查是否是UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(footer)) {
          safeFooter = footer;
        }
      }
      
      const updatedPages = state.pages.map(page => {
        if (page.id === state.currentPageId) {
          return { ...page, footer: safeFooter };
        }
        return page;
      });
      
      return { ...state, pages: updatedPages };
    }),
    
    updateHeaderColor: (color) => set((state) => {
      const updatedPages = state.pages.map(page => {
        if (page.id === state.currentPageId) {
          return { ...page, headerColor: color };
        }
        return page;
      });
      
      return { ...state, pages: updatedPages };
    }),
    
    updateFooterColor: (color) => set((state) => {
      const updatedPages = state.pages.map(page => {
        if (page.id === state.currentPageId) {
          return { ...page, footerColor: color };
        }
        return page;
      });
      
      return { ...state, pages: updatedPages };
    }),
    
    // 元素对齐
    alignElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => set(state => {
      const { pages, currentPageId, selectedElementIds } = state;
      
      // 至少需要选择两个元素才能对齐
      if (selectedElementIds.length < 2) return state;
      
      // 找到当前页面
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = pages[currentPageIndex];
      
      // 获取选中的元素
      const selectedElements = currentPage.elements.filter(el => 
        selectedElementIds.includes(el.id)
      );
      
      // 创建更新后元素的数组
      const updatedElements = [...currentPage.elements];
      
      // 根据对齐类型执行不同的对齐操作
      switch (alignType) {
        case 'left': {
          // 找到最左侧的元素位置
          const leftMost = Math.min(...selectedElements.map(el => el.position.x));
          
          // 更新选中元素的x坐标，使它们左对齐
          selectedElements.forEach(element => {
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  x: leftMost
                }
              };
            }
          });
          break;
        }
        
        case 'center': {
          // 计算每个元素的中心点
          const centers = selectedElements.map(el => {
            const width = el.size?.width || getDefaultWidthForElement(el);
            return el.position.x + width / 2;
          });
          
          // 取第一个元素的中心点作为参考线
          const centerLine = centers[0];
          
          // 更新选中元素的x坐标，使它们中心对齐
          selectedElements.forEach((element, index) => {
            if (index === 0) return; // 跳过第一个元素，保持原位
            
            const width = element.size?.width || getDefaultWidthForElement(element);
            const newX = centerLine - width / 2;
            
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  x: newX
                }
              };
            }
          });
          break;
        }
        
        case 'right': {
          // 找到最右侧的元素位置
          const rightMostElement = selectedElements.reduce((prev, curr) => {
            const prevWidth = prev.size?.width || getDefaultWidthForElement(prev);
            const currWidth = curr.size?.width || getDefaultWidthForElement(curr);
            
            const prevRight = prev.position.x + prevWidth;
            const currRight = curr.position.x + currWidth;
            
            return prevRight > currRight ? prev : curr;
          }, selectedElements[0]);
          
          const rightMost = rightMostElement.position.x + 
            (rightMostElement.size?.width || getDefaultWidthForElement(rightMostElement));
          
          // 更新选中元素的x坐标，使它们右对齐
          selectedElements.forEach(element => {
            if (element.id === rightMostElement.id) return; // 跳过最右侧的元素，保持原位
            
            const width = element.size?.width || getDefaultWidthForElement(element);
            const newX = rightMost - width;
            
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  x: newX
                }
              };
            }
          });
          break;
        }
        
        case 'top': {
          // 找到最顶部的元素位置
          const topMost = Math.min(...selectedElements.map(el => el.position.y));
          
          // 更新选中元素的y坐标，使它们顶部对齐
          selectedElements.forEach(element => {
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  y: topMost
                }
              };
            }
          });
          break;
        }
        
        case 'middle': {
          // 计算每个元素的中心点
          const middles = selectedElements.map(el => {
            const height = el.size?.height || getDefaultHeightForElement(el);
            return el.position.y + height / 2;
          });
          
          // 取第一个元素的中心点作为参考线
          const middleLine = middles[0];
          
          // 更新选中元素的y坐标，使它们中心对齐
          selectedElements.forEach((element, index) => {
            if (index === 0) return; // 跳过第一个元素，保持原位
            
            const height = element.size?.height || getDefaultHeightForElement(element);
            const newY = middleLine - height / 2;
            
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  y: newY
                }
              };
            }
          });
          break;
        }
        
        case 'bottom': {
          // 找到最底部的元素
          const bottomMostElement = selectedElements.reduce((prev, curr) => {
            const prevHeight = prev.size?.height || getDefaultHeightForElement(prev);
            const currHeight = curr.size?.height || getDefaultHeightForElement(curr);
            
            const prevBottom = prev.position.y + prevHeight;
            const currBottom = curr.position.y + currHeight;
            
            return prevBottom > currBottom ? prev : curr;
          }, selectedElements[0]);
          
          const bottomMost = bottomMostElement.position.y + 
            (bottomMostElement.size?.height || getDefaultHeightForElement(bottomMostElement));
          
          // 更新选中元素的y坐标，使它们底部对齐
          selectedElements.forEach(element => {
            if (element.id === bottomMostElement.id) return; // 跳过最底部的元素，保持原位
            
            const height = element.size?.height || getDefaultHeightForElement(element);
            const newY = bottomMost - height;
            
            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex !== -1) {
              updatedElements[elementIndex] = {
                ...updatedElements[elementIndex],
                position: {
                  ...updatedElements[elementIndex].position,
                  y: newY
                }
              };
            }
          });
          break;
        }
      }
      
      // 更新页面元素
      const updatedPages = [...pages];
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: updatedElements
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'arrangement',
        pageId: currentPageId,
        elements: updatedElements,
        previousElements: currentPage.elements
      };
      
      state.addToHistory(historyAction);
      
      return { ...state, pages: updatedPages };
    }),
    
    // 元素分布
    distributeElements: (distributeType: 'horizontal' | 'vertical') => set(state => {
      const { pages, currentPageId, selectedElementIds } = state;
      
      // 至少需要选择三个元素才能进行分布
      if (selectedElementIds.length < 3) return state;
      
      // 找到当前页面
      const currentPageIndex = pages.findIndex(page => page.id === currentPageId);
      if (currentPageIndex === -1) return state;
      
      const currentPage = pages[currentPageIndex];
      
      // 获取选中的元素
      const selectedElements = currentPage.elements.filter(el => 
        selectedElementIds.includes(el.id)
      ).map(el => ({
        ...el,
        width: el.size?.width || getDefaultWidthForElement(el),
        height: el.size?.height || getDefaultHeightForElement(el)
      }));
      
      // 创建更新后元素的数组
      const updatedElements = [...currentPage.elements];
      
      if (distributeType === 'horizontal') {
        // 水平分布：按x坐标排序
        const sortedElements = [...selectedElements].sort((a, b) => a.position.x - b.position.x);
        
        // 获取最左和最右元素的位置
        const leftMost = sortedElements[0].position.x;
        const rightMost = sortedElements[sortedElements.length - 1].position.x + 
                         sortedElements[sortedElements.length - 1].width;
        
        // 计算可用空间和间隔
        const totalWidth = rightMost - leftMost;
        const totalElementsWidth = sortedElements.reduce((sum, el) => sum + el.width, 0);
        const availableSpace = totalWidth - totalElementsWidth;
        const gap = availableSpace / (sortedElements.length - 1);
        
        // 重新分布元素
        let currentX = leftMost;
        sortedElements.forEach((element, index) => {
          if (index === 0) return; // 跳过第一个元素，保持原位
          if (index === sortedElements.length - 1) return; // 跳过最后一个元素，保持原位
          
          // 更新当前元素的x坐标
          currentX = sortedElements[index - 1].position.x + sortedElements[index - 1].width + gap;
          
          const elementIndex = updatedElements.findIndex(el => el.id === element.id);
          if (elementIndex !== -1) {
            updatedElements[elementIndex] = {
              ...updatedElements[elementIndex],
              position: {
                ...updatedElements[elementIndex].position,
                x: currentX
              }
            };
          }
        });
      } else if (distributeType === 'vertical') {
        // 垂直分布：按y坐标排序
        const sortedElements = [...selectedElements].sort((a, b) => a.position.y - b.position.y);
        
        // 获取最上和最下元素的位置
        const topMost = sortedElements[0].position.y;
        const bottomMost = sortedElements[sortedElements.length - 1].position.y + 
                          sortedElements[sortedElements.length - 1].height;
        
        // 计算可用空间和间隔
        const totalHeight = bottomMost - topMost;
        const totalElementsHeight = sortedElements.reduce((sum, el) => sum + el.height, 0);
        const availableSpace = totalHeight - totalElementsHeight;
        const gap = availableSpace / (sortedElements.length - 1);
        
        // 重新分布元素
        let currentY = topMost;
        sortedElements.forEach((element, index) => {
          if (index === 0) return; // 跳过第一个元素，保持原位
          if (index === sortedElements.length - 1) return; // 跳过最后一个元素，保持原位
          
          // 更新当前元素的y坐标
          currentY = sortedElements[index - 1].position.y + sortedElements[index - 1].height + gap;
          
          const elementIndex = updatedElements.findIndex(el => el.id === element.id);
          if (elementIndex !== -1) {
            updatedElements[elementIndex] = {
              ...updatedElements[elementIndex],
              position: {
                ...updatedElements[elementIndex].position,
                y: currentY
              }
            };
          }
        });
      }
      
      // 更新页面元素
      const updatedPages = [...pages];
      updatedPages[currentPageIndex] = {
        ...currentPage,
        elements: updatedElements
      };
      
      // 添加到历史记录
      const historyAction: HistoryAction = {
        type: 'arrangement',
        pageId: currentPageId,
        elements: updatedElements,
        previousElements: currentPage.elements
      };
      
      state.addToHistory(historyAction);
      
      return { ...state, pages: updatedPages };
    }),
    
    // 设置每页题目数量
    setQuestionsPerPage: (count) => set({ questionsPerPage: count }),
    
    // 设置文档版本
    setDocumentVersion: (version) => {
      // 设置文档版本状态
      set(() => ({ 
        documentVersion: version 
      }));
      
      // 记录版本切换
      console.log(`文档版本已切换为：${version}`);
      
      // 因为documentVersion是全局状态，
      // 当它改变时，所有使用这个状态的组件都会自动重新渲染
      // 这样可以确保版本变化应用到所有页面
    }
  };
});

export default usePageStore;