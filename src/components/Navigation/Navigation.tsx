import React, { useEffect } from 'react';
import { VStack, Box, Text, Button, Flex } from '@chakra-ui/react';
import usePageStore from '../../store/pageStore';

const Navigation = () => {
  const { pages, currentPageId, setCurrentPage, documentVersion } = usePageStore();
  
  // 将毫米转换为像素（缩略图尺寸）
  const mmToPx = (mm: number) => mm * 0.5;
  
  // 当版本变化时重新渲染页面预览
  useEffect(() => {
    // 版本切换时记录日志
    console.log("导航栏检测到版本变化:", documentVersion);
    // 不需要额外操作，组件会自动重新渲染
  }, [documentVersion]);
  
  return (
    <VStack p={4} spacing={4} align="stretch">
      <Text fontWeight="bold" fontSize="lg">页面</Text>
      
      {pages.map((page, index) => (
        <Box 
          key={page.id}
          p={2}
          borderWidth={page.id === currentPageId ? 2 : 1}
          borderColor={page.id === currentPageId ? "blue.500" : "gray.200"}
          borderRadius="md"
          cursor="pointer"
          onClick={() => setCurrentPage(page.id)}
        >
          <Flex direction="column" align="center">
            {/* 页面缩略图 */}
            <Box 
              w={`${mmToPx(210)}px`}
              h={`${mmToPx(297)}px`}
              bg="white"
              borderWidth={1}
              borderColor="gray.300"
              position="relative"
              mb={2}
            >
              {/* 这里可以渲染简化版的页面内容 */}
              {page.elements.length === 0 && (
                <Text 
                  position="absolute" 
                  top="50%" 
                  left="50%" 
                  transform="translate(-50%, -50%)"
                  fontSize="xs"
                  color="gray.400"
                >
                  空白页
                </Text>
              )}
              {/* 显示当前版本指示器 */}
              <Text
                position="absolute"
                top="5px"
                right="5px"
                fontSize="6px"
                color={documentVersion === 'teacher' ? "blue.500" : "green.500"}
                fontWeight="bold"
              >
                {documentVersion === 'teacher' ? '教师版' : '学生版'}
              </Text>
            </Box>
            <Text fontSize="sm">第 {index + 1} 页</Text>
          </Flex>
        </Box>
      ))}
    </VStack>
  );
};

export default Navigation;