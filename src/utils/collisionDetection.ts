import { Element } from '../store/types';

/**
 * Function to check if two elements overlap
 * @param element1 First element
 * @param element2 Second element
 * @param margin Optional margin to add around elements (negative value reduces collision area)
 * @returns Boolean indicating if the elements overlap
 */
export const doElementsOverlap = (
  element1: Element, 
  element2: Element, 
  margin: number = -5
): boolean => {
  // Get element dimensions
  const el1Width = element1.size?.width || getDefaultWidthForElement(element1);
  const el1Height = element1.size?.height || getDefaultHeightForElement(element1);
  
  const el2Width = element2.size?.width || getDefaultWidthForElement(element2);
  const el2Height = element2.size?.height || getDefaultHeightForElement(element2);
  
  // Calculate element bounds with margin adjustment
  // Negative margin shrinks the collision box
  const el1Left = element1.position.x - margin;
  const el1Right = element1.position.x + el1Width + margin;
  const el1Top = element1.position.y - margin;
  const el1Bottom = element1.position.y + el1Height + margin;
  
  const el2Left = element2.position.x - margin;
  const el2Right = element2.position.x + el2Width + margin;
  const el2Top = element2.position.y - margin;
  const el2Bottom = element2.position.y + el2Height + margin;
  
  // Check for overlap - if any of these conditions is true, there is NO overlap
  return !(
    el1Right < el2Left ||  // Element 1 is to the left of Element 2
    el1Left > el2Right ||  // Element 1 is to the right of Element 2
    el1Bottom < el2Top ||  // Element 1 is above Element 2
    el1Top > el2Bottom     // Element 1 is below Element 2
  );
};

/**
 * Function to get default width for different element types
 */
export const getDefaultWidthForElement = (element: Element): number => {
  switch (element.type) {
    case 'title':
      return 120; // 标题宽度
    case 'subtitle':
      return 100; // 副标题宽度
    case 'text':
      return 170; // 文本宽度
    case 'question':
      return 100; // 问题宽度
    default:
      return 80; // 默认宽度
  }
};

/**
 * Function to get default height for different element types
 */
export const getDefaultHeightForElement = (element: Element): number => {
  switch (element.type) {
    case 'title':
      return 30; // 标题高度
    case 'subtitle':
      return 20; // 副标题高度
    case 'text':
      // 根据文本长度计算，但限制最大高度为30mm
      if (typeof element.content === 'string') {
        const calculatedHeight = 15 + Math.floor(element.content.length / 150) * 5;
        return Math.min(30, calculatedHeight);
      }
      return 15;
    case 'question':
      // 问题可能有复杂结构，也限制最大高度
      return Math.min(30, 40);
    default:
      return 20; // 默认高度
  }
};

/**
 * Function to find a non-overlapping position for an element
 * @param element Element to position
 * @param otherElements Other elements to check against
 * @param originalPosition Original position to try first
 * @returns New position that doesn't cause overlaps
 */
export const findNonOverlappingPosition = (
  element: Element,
  otherElements: Element[],
  originalPosition: { x: number, y: number }
): { x: number, y: number } => {
  // 首先确保位置不在页眉区域内
  let position = { ...originalPosition };
  const MIN_Y_POSITION = 35; // 页眉高度 
  
  if (position.y < MIN_Y_POSITION) {
    position.y = MIN_Y_POSITION;
  }
  
  // 使用调整后的位置创建测试元素
  const testElement = { ...element, position };
  
  // 如果没有其他元素或是标题(应该居中)，直接返回调整后的位置
  if (otherElements.length === 0 || element.type === 'title') {
    return position;
  }
  
  // Check if this position causes any overlaps
  const hasOverlap = otherElements.some(
    other => other.id !== element.id && doElementsOverlap(testElement, other, -8)
  );
  
  // If no overlap, return the adjusted position
  if (!hasOverlap) {
    return position;
  }
  
  // 获取元素尺寸
  const elementWidth = element.size?.width || getDefaultWidthForElement(element);
  const elementHeight = element.size?.height || getDefaultHeightForElement(element);
  
  // 尝试多种策略来找到合适的位置
  
  // 策略1: 尝试在页面宽度范围内的不同X坐标位置
  const pageWidth = 210; // A4页面宽度(mm)
  const pageMargin = 20; // 页面边距(mm)
  const availableWidth = pageWidth - 2 * pageMargin - elementWidth;
  
  // 在可用宽度范围内尝试不同的X坐标
  for (let testX = pageMargin; testX <= pageMargin + availableWidth; testX += 20) {
    const testPosition = { x: testX, y: Math.max(position.y, MIN_Y_POSITION) };
    const testElementWithNewPos = { ...element, position: testPosition };
    
    // 检查新位置是否会重叠
    const hasOverlapAtNewPos = otherElements.some(
      other => other.id !== element.id && doElementsOverlap(testElementWithNewPos, other, -8)
    );
    
    if (!hasOverlapAtNewPos) {
      return testPosition;
    }
  }
  
  // 策略2: 垂直调整 - 找到最下方元素的底部位置
  const lowestBottom = Math.max(
    ...otherElements.map(el => {
      const height = el.size?.height || getDefaultHeightForElement(el);
      return el.position.y + height;
    }),
    MIN_Y_POSITION // 确保至少从页眉下方开始
  );
  
  // 在最下方元素下面尝试不同的水平位置
  const newY = Math.max(lowestBottom + 5, MIN_Y_POSITION); // 添加一点间距，确保在页眉下方
  
  for (let testX = pageMargin; testX <= pageMargin + availableWidth; testX += 20) {
    const testPosition = { x: testX, y: newY };
    const testElementWithNewPos = { ...element, position: testPosition };
    
    // 检查新位置是否会重叠
    const hasOverlapAtNewPos = otherElements.some(
      other => other.id !== element.id && doElementsOverlap(testElementWithNewPos, other, -8)
    );
    
    if (!hasOverlapAtNewPos) {
      return testPosition;
    }
  }
  
  // 策略3: 递增Y轴，直到找到不重叠的位置
  for (let testY = newY + 10; testY < newY + 100; testY += 10) {
    const testPosition = { x: position.x, y: testY };
    const testElementWithNewPos = { ...element, position: testPosition };
    
    // 检查新位置是否会重叠
    const hasOverlapAtNewPos = otherElements.some(
      other => other.id !== element.id && doElementsOverlap(testElementWithNewPos, other, -8)
    );
    
    if (!hasOverlapAtNewPos) {
      return testPosition;
    }
  }
  
  // 最后手段: 返回在底部的位置，几乎肯定不会重叠
  return { x: position.x, y: Math.max(lowestBottom + 20, MIN_Y_POSITION) };
}; 