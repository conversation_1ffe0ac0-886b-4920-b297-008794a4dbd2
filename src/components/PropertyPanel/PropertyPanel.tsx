import React from 'react';
import { VStack, Box, Text, Heading, Divider, HStack, Button, useColorModeValue, RadioGroup, Radio, Stack, useToast, SimpleGrid, Card, CardHeader, CardBody, Flex, Icon } from '@chakra-ui/react';
import { MdColorLens, MdFormatListNumbered, MdInput, MdTextFields, MdImportContacts, MdSpellcheck } from 'react-icons/md';
import DraggableItem from './DraggableItem';
import ImportPanel from './ImportPanel';
import ProofreadPanel from './ProofreadPanel';
import usePageStore from '../../store/pageStore';
import FontSizeSelector from './FontSizeSelector';

const PropertyPanel = () => {
  const { 
    selectedElementIds, 
    questionsPerPage, 
    setQuestionsPerPage, 
    documentVersion, 
    setDocumentVersion, 
    arrangeElementsWaterfall,
    updateHeaderColor,
    updateFooterColor
  } = usePageStore();
  const hasSelection = selectedElementIds.length > 0;
  const toast = useToast();
  const cardBg = useColorModeValue('white', 'gray.700');
  const headerBg = useColorModeValue('gray.50', 'gray.800');
  
  // 定义可用的颜色选项
  const colorOptions = [
    { name: '橙', value: 'orange.300' },
    { name: '黄', value: 'yellow.300' },
    { name: '红', value: 'red.300' },
    { name: '蓝', value: 'blue.300' },
    { name: '绿', value: 'green.300' }
  ];
  
  // 统一设置页眉页脚颜色
  const handleColorChange = (color: string) => {
    updateHeaderColor(color);
    updateFooterColor(color);
    toast({
      title: "页眉页脚颜色已更新",
      status: "success",
      duration: 2000,
      isClosable: true,
    });
  };
  
  const handleQuestionsPerPageChange = (value: string) => {
    setQuestionsPerPage(parseInt(value));
  };
  
  // 设置每页题目数量并应用布局
  const handleSetQuestionsAndApplyLayout = (count: number) => {
    setQuestionsPerPage(count);
    
    // 如果是1题/页或2题/页，自动应用布局
    if (count > 0) {
      arrangeElementsWaterfall();
      toast({
        title: "布局已应用",
        description: `已设置为${count}题/页并自动排列`,
        status: "success",
        duration: 2000,
        isClosable: true,
      });
    }
  };

  return (
    <VStack p={4} spacing={6} align="stretch">
      
      <Card variant="elevated" shadow="md" bg={cardBg}>
        <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
          <Flex align="center">
            <Icon as={MdImportContacts} mr={2} color="teal.500" />
            <Text fontWeight="bold">文档导入</Text>
          </Flex>
        </CardHeader>
        <CardBody py={4}>
          <ImportPanel />
        </CardBody>
      </Card>
      
      <Card variant="elevated" shadow="md" bg={cardBg}>
        <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
          <Flex align="center">
            <Icon as={MdTextFields} mr={2} color="blue.500" />
            <Text fontWeight="bold">基础组件</Text>
          </Flex>
        </CardHeader>
        <CardBody py={4}>
          <VStack spacing={3} align="stretch">
            <DraggableItem type="text" label="文本框" />
            <DraggableItem type="title" label="标题" />
            <DraggableItem type="subtitle" label="小标题" />
            <DraggableItem type="number" label="序号" />
          </VStack>
        </CardBody>
      </Card>
      
      {hasSelection && (
        <Card variant="elevated" shadow="md" bg={cardBg}>
          <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
            <Flex align="center">
              <Icon as={MdTextFields} mr={2} color="purple.500" />
              <Text fontWeight="bold">文字大小</Text>
            </Flex>
          </CardHeader>
          <CardBody py={4}>
            <FontSizeSelector />
          </CardBody>
        </Card>
      )}
      
      <Card variant="elevated" shadow="md" bg={cardBg}>
        <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
          <Flex align="center">
            <Icon as={MdFormatListNumbered} mr={2} color="orange.500" />
            <Text fontWeight="bold">每页题目数量</Text>
          </Flex>
        </CardHeader>
        <CardBody py={4}>
          <HStack spacing={3}>
            <Button 
              size="sm"
              colorScheme="gray"
              onClick={() => handleSetQuestionsAndApplyLayout(1)}
            >
              1题/页
            </Button>
            <Button 
              size="sm"
              colorScheme="gray"
              onClick={() => handleSetQuestionsAndApplyLayout(2)}
            >
              2题/页
            </Button>
          </HStack>
          <Text fontSize="xs" color="gray.500" mt={2}>
            选择"1题/页"或"2题/页"会自动应用布局
          </Text>
        </CardBody>
      </Card>
      
      <Card variant="elevated" shadow="md" bg={cardBg}>
        <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
          <Flex align="center">
            <Icon as={MdColorLens} mr={2} color="red.500" />
            <Text fontWeight="bold">页眉页脚颜色</Text>
          </Flex>
        </CardHeader>
        <CardBody py={4} pb={6}>
          <Flex justify="space-between" align="center" mx={2}>
            {colorOptions.map((color) => (
              <Button 
                key={color.value}
                bg={color.value}
                color="gray.800"
                fontWeight="bold"
                _hover={{ bg: color.value, opacity: 0.8 }}
                onClick={() => handleColorChange(color.value)}
                shadow="sm"
                borderRadius="full"
                width="45px"
                height="45px"
                p={0}
              >
                {color.name}
              </Button>
            ))}
          </Flex>
        </CardBody>
      </Card>
      
      <Card variant="elevated" shadow="md" bg={cardBg}>
        <CardHeader py={2} px={4} bg={headerBg} borderTopRadius="md">
          <Flex align="center">
            <Icon as={MdSpellcheck} mr={2} color="cyan.500" />
            <Text fontWeight="bold">文本校对</Text>
          </Flex>
        </CardHeader>
        <CardBody py={4}>
          <ProofreadPanel />
        </CardBody>
      </Card>
    </VStack>
  );
};

export default PropertyPanel;