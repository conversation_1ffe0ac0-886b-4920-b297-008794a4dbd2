import React, { useState, useRef, useEffect } from 'react';
import { Box, Text, Flex, Icon } from '@chakra-ui/react';
import { CheckCircleIcon } from '@chakra-ui/icons';
import usePageStore, { Element } from '../../store/pageStore';

// 调整大小句柄的尺寸（像素）
const HANDLE_SIZE = 12;

interface SubtitleElementProps {
  element: Element;
  isSelected?: boolean;
}

const SubtitleElement: React.FC<SubtitleElementProps> = ({ element, isSelected = false }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const { updateElement } = usePageStore();
  const [text, setText] = useState(element.content || '方法总结');
  
  // 引用存储调整前的尺寸和鼠标位置
  const resizeStartRef = useRef({ width: 0, mouseX: 0 });
  // 引用元素的DOM
  const elementRef = useRef<HTMLDivElement>(null);
  
  // 毫米转像素和像素转毫米的辅助函数
  const mmToPx = (mm: number) => mm * 3.7795275591;
  const pxToMm = (px: number) => px / 3.7795275591;
  
  const handleDoubleClick = () => {
    setIsEditing(true);
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setText(e.target.value);
  };
  
  const handleBlur = () => {
    updateElement(element.id, { content: text });
    setIsEditing(false);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 只有按下Enter键时才完成编辑
    if (e.key === 'Enter') {
      handleBlur();
    }
    // 避免退格键冒泡到Canvas
    if (e.key === 'Backspace') {
      e.stopPropagation();
    }
  };
  
  // 开始调整大小
  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsResizing(true);
    
    // 获取当前元素宽度
    const width = elementRef.current?.offsetWidth || 0;
    
    // 保存调整开始时的宽度和鼠标位置
    resizeStartRef.current = {
      width,
      mouseX: e.clientX
    };
    
    // 添加全局鼠标事件监听器
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };
  
  // 调整大小过程中
  const handleResizeMove = (e: MouseEvent) => {
    if (!isResizing) return;
    
    // 阻止事件冒泡和默认行为
    e.preventDefault();
    e.stopPropagation();
    
    // 计算鼠标移动距离
    const deltaX = e.clientX - resizeStartRef.current.mouseX;
    
    // 计算新宽度（保持最小宽度）
    const newWidth = Math.max(100, resizeStartRef.current.width + deltaX);
    
    // 直接更新DOM元素的样式，提高响应速度
    if (elementRef.current) {
      elementRef.current.style.width = `${newWidth}px`;
    }
  };
  
  // 结束调整大小
  const handleResizeEnd = () => {
    if (!isResizing) return;
    setIsResizing(false);
    
    // 移除全局鼠标事件监听器
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
  };
  
  // 清理事件监听器
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, []);
  
  // 根据字号属性返回相应的字体大小
  const getFontSize = () => {
    switch (element.fontSize) {
      case 'small':
        return '14px';
      case 'large':
        return '22px';
      case 'medium':
      default:
        return '18px';
    }
  };
  
  return (
    <Box
      ref={elementRef}
      position="relative"
      bg="green.500"
      borderRadius="md"
      px={4}
      py={1}
      minW="120px"
      maxW="300px"
      onDoubleClick={handleDoubleClick}
      cursor={isResizing ? "ew-resize" : "default"}
    >
      <Flex alignItems="center" justifyContent="flex-start">
        <Icon as={CheckCircleIcon} color="white" w={4} h={4} mr={2} />
        
        {isEditing ? (
          <input
            value={text}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            autoFocus
            style={{
              background: 'transparent',
              border: 'none',
              color: 'white',
              fontWeight: 'medium',
              width: '100%',
              fontSize: getFontSize(),
              outline: 'none'
            }}
          />
        ) : (
          <Text color="white" fontWeight="medium" fontSize={getFontSize()}>
            {text}
          </Text>
        )}
      </Flex>
      
      {(isSelected || isResizing) && (
        <>
          <Box
            position="absolute"
            right={`-${HANDLE_SIZE / 2}px`}
            top="50%"
            transform="translateY(-50%)"
            width={`${HANDLE_SIZE}px`}
            height={`${HANDLE_SIZE}px`}
            bg="blue.500"
            borderRadius="full"
            cursor="ew-resize"
            onMouseDown={handleResizeStart}
            opacity={0.7}
            _hover={{ opacity: 1 }}
            zIndex={10}
          />
          <Box
            position="absolute"
            left={`-${HANDLE_SIZE / 2}px`}
            top="50%"
            transform="translateY(-50%)"
            width={`${HANDLE_SIZE}px`}
            height={`${HANDLE_SIZE}px`}
            bg="blue.500"
            borderRadius="full"
            cursor="ew-resize"
            onMouseDown={handleResizeStart}
            opacity={0.7}
            _hover={{ opacity: 1 }}
            zIndex={10}
          />
        </>
      )}
    </Box>
  );
};

export default SubtitleElement; 