import React, { useEffect, useState } from 'react';
import { Flex, Box, Text, Input, Image } from '@chakra-ui/react';
import usePageStore from '../../store/pageStore';

interface DecorativeHeaderProps {
  pageNumber: number;
  customText?: string;
  color?: string;
}

// 正确引用public目录中的图片
const danJunLogo = '/danjun.png';

const DecorativeHeader: React.FC<DecorativeHeaderProps> = ({ pageNumber, customText, color = "yellow.200" }) => {
  const { updatePageHeader } = usePageStore();
  const [isEditing, setIsEditing] = useState(false);
  const [inputText, setInputText] = useState('');
  // 使用本地状态来存储处理后的文本
  const [displayText, setDisplayText] = useState<string>(`页面 ${pageNumber}`);
  
  // 当customText或pageNumber变化时更新显示文本
  useEffect(() => {
    try {
      if (!customText || typeof customText !== 'string') {
        setDisplayText(`页面 ${pageNumber}`);
        setInputText(`页面 {{pageNumber}}`);
        return;
      }
      
      // 检查是否是UUID格式
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(customText)) {
        setDisplayText(`页面 ${pageNumber}`);
        setInputText(`页面 {{pageNumber}}`);
        return;
      }
      
      // 替换页码标记
      const processedText = customText.replace('{{pageNumber}}', String(pageNumber));
      setDisplayText(processedText);
      setInputText(customText);
    } catch (error) {
      console.error('处理页眉文本时出错:', error);
      setDisplayText(`页面 ${pageNumber}`);
      setInputText(`页面 {{pageNumber}}`);
    }
  }, [customText, pageNumber]);

  // 算术符号和对应的背景色
  const symbols = [
    { symbol: '+', bgColor: 'orange.400' },
    { symbol: '?', bgColor: 'yellow.400' },
    { symbol: '-', bgColor: 'green.400' },
    { symbol: '÷', bgColor: 'green.500' },
    { symbol: '×', bgColor: 'blue.400' },
    { symbol: '?', bgColor: 'blue.300' },
  ];

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    updatePageHeader(inputText);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputText(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 只有按下Enter键时才完成编辑
    if (e.key === 'Enter') {
      handleBlur();
    }
    // 避免退格键冒泡到Canvas
    if (e.key === 'Backspace') {
      e.stopPropagation();
    }
  };

  return (
    <Box width="100%" position="absolute" top="0" left="0">
      {/* 动态背景色 */}
      <Box bg={color} height="20mm" width="100%" position="relative">
        {/* 符号色块 */}
        <Flex position="absolute" bottom="-5px" left="10mm" transform="translateY(50%)">
          {symbols.map((item, index) => (
            <Box 
              key={index}
              bg={item.bgColor}
              color="white"
              fontWeight="bold"
              fontSize="16px"
              width="35px"
              height="35px"
              display="flex"
              alignItems="center"
              justifyContent="center"
              marginRight="2"
            >
              {item.symbol}
            </Box>
          ))}
        </Flex>
        
        {/* 自定义文本区域 - 左上角 */}
        <Box 
          position="absolute" 
          top="3mm" 
          left="10mm" 
          fontWeight="medium"
          onDoubleClick={handleDoubleClick}
          cursor="pointer"
        >
          {isEditing ? (
            <Input
              value={inputText}
              onChange={handleChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              autoFocus
              size="sm"
              width="auto"
              minWidth="150px"
              bg="white"
              border="1px solid"
              borderColor="gray.300"
            />
          ) : (
            <Text>
              {displayText}
            </Text>
          )}
        </Box>
        
        {/* 右上角丹钧图片 */}
        <Box 
          position="absolute" 
          top="2mm" 
          right="10mm"
          height="16mm"
        >
          <Image 
            src={danJunLogo} 
            alt="丹钧" 
            height="100%" 
            objectFit="contain" 
          />
        </Box>
      </Box>
    </Box>
  );
};

export default DecorativeHeader;