// 定义校对规则类型
interface ProofreadRule {
  pattern: RegExp;
  replacement: string;
  description: string;
}

// 定义校对结果类型
export interface ProofreadResult {
  text: string;
  corrections: {
    original: string;
    corrected: string;
    description: string;
    position: number;
  }[];
  hasCorrections: boolean;
}

// 标点符号校对规则
const punctuationRules: ProofreadRule[] = [
  // 中文后面的英文标点替换为中文标点
  { pattern: /([[\u4e00-\u9fa5])\.(?!\d)/g, replacement: '$1。', description: '中文后的英文句号替换为中文句号' },
  { pattern: /([[\u4e00-\u9fa5]),/g, replacement: '$1，', description: '中文后的英文逗号替换为中文逗号' },
  { pattern: /([[\u4e00-\u9fa5]):/g, replacement: '$1：', description: '中文后的英文冒号替换为中文冒号' },
  { pattern: /([[\u4e00-\u9fa5]);/g, replacement: '$1；', description: '中文后的英文分号替换为中文分号' },
  { pattern: /([[\u4e00-\u9fa5])\?/g, replacement: '$1？', description: '中文后的英文问号替换为中文问号' },
  { pattern: /([[\u4e00-\u9fa5])!/g, replacement: '$1！', description: '中文后的英文感叹号替换为中文感叹号' },
  { pattern: /([[\u4e00-\u9fa5])\(/g, replacement: '$1（', description: '中文后的英文左括号替换为中文左括号' },
  { pattern: /([[\u4e00-\u9fa5])\)/g, replacement: '$1）', description: '中文后的英文右括号替换为中文右括号' },
  
  // 中英文之间删除空格
  { pattern: /([[\u4e00-\u9fa5])\s+([a-zA-Z])/g, replacement: '$1$2', description: '删除中文与英文之间的空格' },
  { pattern: /([a-zA-Z])\s+([[\u4e00-\u9fa5])/g, replacement: '$1$2', description: '删除英文与中文之间的空格' },
  
  // 连续多个标点符号的处理
  { pattern: /。{2,}/g, replacement: '……', description: '连续句号替换为省略号' },
  { pattern: /！{2,}/g, replacement: '！', description: '连续感叹号替换为单个感叹号' },
  { pattern: /？{2,}/g, replacement: '？', description: '连续问号替换为单个问号' },
  { pattern: /，{2,}/g, replacement: '，', description: '连续逗号替换为单个逗号' },
  
  // 全角与半角处理
  { pattern: /　/g, replacement: ' ', description: '全角空格替换为半角空格' },
  
  // 简单的数学公式标点处理
  { pattern: /(\d)x(\d)/g, replacement: '$1×$2', description: '将小写字母x替换为乘号×' },
  { pattern: /(\d)\*(\d)/g, replacement: '$1×$2', description: '将星号*替换为乘号×' },
  
  // 数字和符号之间删除空格
  { pattern: /(\d)\s+([+\-×÷=<>≤≥≈≠%‰°′″℃$¥€£¢])/g, replacement: '$1$2', description: '删除数字与符号之间的空格' },
  { pattern: /([+\-×÷=<>≤≥≈≠%‰°′″℃$¥€£¢])\s+(\d)/g, replacement: '$1$2', description: '删除符号与数字之间的空格' },
];

// 常见错别字和用词规则
const spellingRules: ProofreadRule[] = [
  // 常见错别字校正
  { pattern: /([^课])程序/g, replacement: '$1过程', description: '常见用词修正：程序->过程' },
  { pattern: /解析：/g, replacement: '解答：', description: '常见用词修正：解析->解答' },
  { pattern: /如果说/g, replacement: '如果', description: '冗余表达：删除"如果说"中的"说"' },
  { pattern: /有可能/g, replacement: '可能', description: '冗余表达：删除"有可能"中的"有"' },
  { pattern: /总结归纳/g, replacement: '归纳总结', description: '常见搭配：总结归纳->归纳总结' },
  { pattern: /原因是?因为/g, replacement: '原因是', description: '冗余表达：原因是因为->原因是' },
  { pattern: /互相之间/g, replacement: '互相', description: '冗余表达：删除"互相之间"中的"之间"' },
  { pattern: /往回返/g, replacement: '返回', description: '冗余表达：往回返->返回' },
  { pattern: /不可缺少/g, replacement: '不可或缺', description: '常见用词修正：不可缺少->不可或缺' },
  { pattern: /甚至于/g, replacement: '甚至', description: '冗余表达：删除"甚至于"中的"于"' },
  { pattern: /首当其冲/g, replacement: '首当其冲', description: '常见用词修正：首当其冲->首当其冲' },
  { pattern: /为了避免防止/g, replacement: '为了避免', description: '冗余表达：为了避免防止->为了避免' },
  { pattern: /比较比/g, replacement: '比', description: '冗余表达：比较比->比' },
  { pattern: /所有的方面/g, replacement: '所有方面', description: '冗余表达：所有的方面->所有方面' },
  { pattern: /(^|[^数])图像/g, replacement: '$1图象', description: '数学领域规范用词：图像→图象' },
  { pattern: /(其)[它]/g, replacement: '$1他', description: '规范用词：其它→其他' },
  
  // 常见数学用词修正
  { pattern: /解方程组/g, replacement: '解方程组', description: '常见用词检查' },
  { pattern: /三角型/g, replacement: '三角形', description: '常见错别字：三角型->三角形' },
  { pattern: /平方米/g, replacement: '平方米', description: '单位检查：平方米' },
  { pattern: /立方米/g, replacement: '立方米', description: '单位检查：立方米' },
  { pattern: /长方型/g, replacement: '长方形', description: '常见错别字：长方型->长方形' },
  { pattern: /正方型/g, replacement: '正方形', description: '常见错别字：正方型->正方形' },
  { pattern: /椭圆型/g, replacement: '椭圆形', description: '常见错别字：椭圆型->椭圆形' },
  { pattern: /最大值点/g, replacement: '极大值点', description: '数学术语：最大值点->极大值点' },
  { pattern: /最小值点/g, replacement: '极小值点', description: '数学术语：最小值点->极小值点' },
];

// 排版规则
const formattingRules: ProofreadRule[] = [
  // 空格处理
  { pattern: / {2,}/g, replacement: ' ', description: '删除多余空格' },
  { pattern: /([。，；：？！）】」》』\)]) /g, replacement: '$1', description: '删除中文标点后的空格' },
  { pattern: / ([（【「《『\(])/g, replacement: '$1', description: '删除中文标点前的空格' },
  
  // 段落空行处理
  { pattern: /\n{3,}/g, replacement: '\n\n', description: '将连续三个及以上换行符替换为两个' },
  
  // 特殊格式处理
  { pattern: /(?:\(|\（)(\d+)(?:\)|\）)/g, replacement: '（$1）', description: '统一序号格式为中文括号' },
];

// 合并所有规则
const allRules = [...punctuationRules, ...spellingRules, ...formattingRules];

/**
 * 自动校对文本内容
 * @param text 需要校对的文本
 * @returns 校对结果
 */
export function proofreadText(text: string): ProofreadResult {
  const corrections: ProofreadResult['corrections'] = [];
  let correctedText = text;
  
  allRules.forEach(rule => {
    // 收集所有匹配项及其位置
    const matches: { match: RegExpExecArray, corrected: string }[] = [];
    let match: RegExpExecArray | null;
    const regex = new RegExp(rule.pattern);
    
    while ((match = regex.exec(correctedText)) !== null) {
      // 避免无限循环
      if (match.index === regex.lastIndex) {
        regex.lastIndex++;
      }
      
      const corrected = match[0].replace(rule.pattern, rule.replacement);
      matches.push({ match, corrected });
    }
    
    // 从后向前替换，以避免位置变化
    for (let i = matches.length - 1; i >= 0; i--) {
      const matchObj = matches[i];
      if (!matchObj) continue;
      
      const { match, corrected } = matchObj;
      if (!match || corrected === undefined) continue;
      
      const position = match.index || 0;
      
      // 添加到修正列表
      if (match[0] !== corrected) {
        corrections.push({
          original: match[0],
          corrected,
          description: rule.description,
          position
        });
      }
    }
    
    // 执行替换
    correctedText = correctedText.replace(rule.pattern, rule.replacement);
  });
  
  return {
    text: correctedText,
    corrections: corrections.sort((a, b) => a.position - b.position),
    hasCorrections: corrections.length > 0
  };
}

/**
 * 自动校对多段文本内容
 * @param texts 需要校对的文本数组
 * @returns 校对结果数组
 */
export function proofreadTexts(texts: string[]): ProofreadResult[] {
  return texts.map(text => proofreadText(text));
} 