import React, { useState, useMemo, useEffect } from 'react';
import { Box, Center, Text, Flex, Input, HStack, Badge } from '@chakra-ui/react';
import usePageStore, { Element } from '../../store/pageStore';

// 辅助函数：从文本中提取序号
const extractNumberPart = (text: string): string => {
  // 如果是单个数字，直接返回
  if (/^[0-9]$/.test(text)) {
    return text;
  }
  
  // 提取开头的数字部分
  const match = text.match(/^([0-9]+)[.、\s]/);
  if (match && match[1]) {
    return match[1];
  }
  
  // 默认返回1
  return "1";
};

// 辅助函数：从文本中提取内容部分
const extractContentPart = (text: string): string => {
  // 如果是单个数字，返回空字符串
  if (/^[0-9]$/.test(text)) {
    return "";
  }
  
  // 去除开头的数字和分隔符，保留实际内容
  const match = text.match(/^[0-9]+[.、\s](.*)/);
  if (match && match[1]) {
    return match[1].trim();
  }
  
  // 默认返回空文本
  return "";
};

// 辅助函数：根据字号属性获取字体大小
const getFontSize = (fontSize?: 'small' | 'medium' | 'large'): string => {
  switch (fontSize) {
    case 'small':
      return '14px';
    case 'large':
      return '24px';
    case 'medium':
    default:
      return '16px';
  }
};

// 辅助函数：根据字号属性获取圆圈大小
const getCircleSize = (fontSize?: 'small' | 'medium' | 'large'): string => {
  switch (fontSize) {
    case 'small':
      return '20px';
    case 'large':
      return '32px';
    case 'medium':
    default:
      return '24px';
  }
};

interface NumberElementProps {
  element: Element;
  isSelected?: boolean;
}

const NumberElement: React.FC<NumberElementProps> = ({ element, isSelected = false }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingContent, setEditingContent] = useState(element.content);
  const { updateElement, documentVersion } = usePageStore();
  
  // 分离序号和内容
  const numberPart = useMemo(() => extractNumberPart(element.content), [element.content]);
  const contentPart = useMemo(() => extractContentPart(element.content), [element.content]);
  
  // 获取字体大小和圆圈大小
  const fontSize = getFontSize(element.fontSize);
  const circleSize = getCircleSize(element.fontSize);
  
  // 调试日志
  useEffect(() => {
    console.log(`NumberElement 渲染: id=${element.id}, content=${element.content}, position=${JSON.stringify(element.position)}`);
  }, [element.id, element.content, element.position]);
  
  const handleDoubleClick = () => {
    // 确保编辑时显示完整内容
    setEditingContent(element.content);
    setIsEditing(true);
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditingContent(e.target.value);
  };
  
  const handleBlur = () => {
    // 检查输入内容格式
    const input = editingContent;
    
    // 如果输入内容为空或只有空格，保持原内容
    if (!input || input.trim() === '') {
      setIsEditing(false);
      setEditingContent(element.content);
      return;
    }
    
    // 检查输入内容是否已经包含了序号格式
    const hasNumberPrefix = /^[0-9]+[.、\s]/.test(input);
    
    // 如果没有序号格式，自动添加原序号
    let finalContent;
    if (!hasNumberPrefix) {
      // 保持原序号，添加新内容
      finalContent = `${numberPart}. ${input}`;
    } else {
      // 已有序号格式，直接使用输入内容
      finalContent = input;
    }
    
    // 更新元素内容
    updateElement(element.id, { content: finalContent });
    setIsEditing(false);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 只有按下Enter键时才完成编辑
    if (e.key === 'Enter') {
      handleBlur();
    }
    // 避免退格键冒泡到Canvas
    if (e.key === 'Backspace') {
      e.stopPropagation();
    }
  };
  
  // 根据版本确定样式
  const bgColor = documentVersion === 'teacher' ? "orange.400" : "red.500";
  
  return (
    <Flex 
      width="100%" 
      maxWidth="160mm" 
      align="flex-start" 
      position="relative"
      data-element-type="number"
      data-element-id={element.id}
    >
      {/* 序号部分 */}
      <Center
        boxSize={circleSize}
        borderRadius="50%"
        bg={bgColor}
        color="white"
        fontWeight="bold"
        fontSize={fontSize}
        boxShadow="sm"
        userSelect="none"
        onDoubleClick={handleDoubleClick}
        cursor={isEditing ? "text" : "default"}
        overflow="hidden"
        flexShrink={0}
        mr={3}
        position="relative"
      >
        <Text fontSize={fontSize} fontWeight="bold" lineHeight="1">
          {numberPart}
        </Text>
        
        {/* 添加版本指示器 */}
        {documentVersion === 'student' && (
          <Badge 
            position="absolute" 
            top="0" 
            right="0" 
            size="xs" 
            colorScheme="green"
            transform="translate(50%, -50%)"
          >
            S
          </Badge>
        )}
      </Center>
      
      {/* 内容部分 */}
      {!isEditing && (
        <Box
          onDoubleClick={handleDoubleClick}
          cursor="pointer"
          fontSize={fontSize}
          width="calc(100% - 35px)"
          overflow="visible"
          mt="1px"
        >
          {contentPart && (
            <Text textAlign="left" whiteSpace="pre-wrap" overflowWrap="break-word">
              {contentPart}
            </Text>
          )}
        </Box>
      )}
      
      {/* 编辑模式 */}
      {isEditing && (
        <Box 
          position="absolute" 
          left="0" 
          top="0" 
          right="0" 
          bottom="0" 
          bg="white" 
          zIndex={2}
          p={2}
          borderRadius="md"
          boxShadow="md"
          width="100%"
        >
          <Flex direction="column">
            <Text fontSize="xs" mb={1} color="gray.500">
              {numberPart}. 的格式会被保留，您可以直接输入内容或更改完整格式
            </Text>
            <Input
              autoFocus
              value={editingContent}
              onChange={handleChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              placeholder={`${numberPart}. 在此输入内容`}
              fontSize={fontSize}
            />
          </Flex>
        </Box>
      )}
    </Flex>
  );
};

export default NumberElement; 