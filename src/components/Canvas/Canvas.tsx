import React, { useRef, useCallback, useState, useEffect } from 'react';
import { Box, Text, Flex, Button, Tooltip, Icon, useToast, HStack, IconButton, Menu, MenuButton, MenuList, MenuItem, Divider } from '@chakra-ui/react';
import { useDrop } from 'react-dnd';
import { BsWater, BsArrowUp, BsArrowDown, BsArrowsExpand, BsGrid1X2, BsGrid3X2 } from 'react-icons/bs';
import { FiCopy, FiTrash2, FiScissors, FiClipboard, FiRotateCcw, FiRotateCw, FiAlignLeft, FiAlignCenter, FiAlignRight } from 'react-icons/fi';
import usePageStore, { Element } from '../../store/pageStore';
import TextElement from './TextElement';
import QuestionElement from './QuestionElement';
import DecorativeHeader from './DecorativeHeader';
import DecorativeFooter from './DecorativeFooter';
import DraggableElement from './DraggableElement';
import TitleElement from './TitleElement';
import ImageElement from './ImageElement';
import SubtitleElement from './SubtitleElement';
import NumberElement from './NumberElement';
import { findNonOverlappingPosition } from '../../utils/collisionDetection';

const Canvas = () => {
  // 使用可变引用来允许在回调中修改current值
  const canvasRef = useRef<HTMLDivElement | null>(null);
  const { 
    pages, 
    currentPageId, 
    addElement, 
    updateElement, 
    arrangeElementsWaterfall,
    selectedElementIds,
    selectElement,
    clearSelection,
    deleteSelectedElements,
    copySelectedElements,
    cutSelectedElements,
    pasteElements,
    undo,
    redo,
    alignElements,
    documentVersion,
    questionsPerPage
  } = usePageStore();
  const toast = useToast();
  
  // 鼠标位置状态, 用于粘贴时定位
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  const currentPage = pages.find(page => page.id === currentPageId) || pages[0];
  
  // 处理元素拖动 - 取消自动位置调整
  const handleElementDrag = useCallback((id: string, position: { x: number, y: number }) => {
    const currentElement = currentPage.elements.find(el => el.id === id);
    if (!currentElement) return;
    
    // 确保元素不能放置在页眉区域
    if (position.y < 35) {
      position.y = 35;
      toast({
        title: "位置限制",
        description: "无法将元素放置在页眉区域",
        status: "warning",
        duration: 2000,
        isClosable: true,
      });
    }
    
    // 确保元素不能放置在页脚区域
    const PAGE_HEIGHT = 297; // A4纸高度（mm）
    const PAGE_MARGIN_BOTTOM = 20; // 底部边距（mm）
    const bottomProtectionArea = PAGE_HEIGHT - PAGE_MARGIN_BOTTOM; // 底部保护区域起始位置
    
    // 获取元素高度
    const elementHeight = currentElement.size?.height || 
      (currentElement.type === 'title' ? 30 : 
      currentElement.type === 'subtitle' ? 20 : 
      currentElement.type === 'image' ? 105 : 15);
    
    // 计算元素底部位置
    const elementBottom = position.y + elementHeight;
    
    // 如果元素底部超过保护区域，调整位置
    if (elementBottom > bottomProtectionArea) {
      position.y = bottomProtectionArea - elementHeight;
      toast({
        title: "位置限制",
        description: "无法将元素放置在页脚区域",
        status: "warning",
        duration: 2000,
        isClosable: true,
      });
    }
    
    // 直接更新元素位置，不进行碰撞检测和位置调整
    updateElement(id, { position: position });
  }, [currentPage.elements, updateElement, toast]);
  
  // 元素点击处理
  const handleElementClick = useCallback((id: string, e: React.MouseEvent) => {
    // 如果按住Shift键，切换选择
    if (e.shiftKey) {
      selectElement(id, true);
    } else {
      // 否则单选
      selectElement(id);
    }
    e.stopPropagation();
  }, [selectElement]);
  
  // 画布点击处理 - 清除选择
  const handleCanvasClick = useCallback(() => {
    clearSelection();
  }, [clearSelection]);
  
  // 键盘快捷键处理
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // 只有在焦点在画布上时处理快捷键
    if (!canvasRef.current?.contains(document.activeElement)) return;
    
    // 复制 - Ctrl+C 或 Cmd+C
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
      copySelectedElements();
      e.preventDefault();
      toast({
        title: "已复制",
        description: `已复制 ${selectedElementIds.length} 个元素`,
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
    
    // 剪切 - Ctrl+X 或 Cmd+X
    if ((e.ctrlKey || e.metaKey) && e.key === 'x') {
      cutSelectedElements();
      e.preventDefault();
      toast({
        title: "已剪切",
        description: `已剪切 ${selectedElementIds.length} 个元素`,
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
    
    // 粘贴 - Ctrl+V 或 Cmd+V
    if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
      pasteElements(mousePosition);
      e.preventDefault();
      toast({
        title: "已粘贴",
        description: "已粘贴元素",
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
    
    // 删除 - Delete 或 Backspace
    if (e.key === 'Delete' || e.key === 'Backspace') {
      deleteSelectedElements();
      e.preventDefault();
      toast({
        title: "已删除",
        description: "已删除选中元素",
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
    
    // 撤销 - Ctrl+Z 或 Cmd+Z
    if ((e.ctrlKey || e.metaKey) && !e.shiftKey && e.key === 'z') {
      undo();
      e.preventDefault();
      toast({
        title: "撤销",
        description: "已撤销上一步操作",
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
    
    // 重做 - Ctrl+Y 或 Cmd+Shift+Z
    if (((e.ctrlKey || e.metaKey) && e.key === 'y') || 
        ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z')) {
      redo();
      e.preventDefault();
      toast({
        title: "重做",
        description: "已重做操作",
        status: "info",
        duration: 1000,
        isClosable: true,
      });
    }
  }, [
    copySelectedElements, 
    cutSelectedElements, 
    pasteElements, 
    deleteSelectedElements, 
    undo, 
    redo, 
    selectedElementIds, 
    toast, 
    mousePosition
  ]);
  
  // 鼠标移动跟踪
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const x = (e.clientX - rect.left) * 0.264583; // px转mm
      const y = (e.clientY - rect.top) * 0.264583; // px转mm
      setMousePosition({ x, y });
    }
  }, []);
  
  // 设置和清理事件监听器
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [handleKeyDown, handleMouseMove]);

  const [{ isOver }, dropRef] = useDrop({
    accept: ['TEXT', 'QUESTION', 'TITLE', 'SUBTITLE', 'IMAGE', 'NUMBER'],
    drop: (item: any, monitor) => {
      const offset = monitor.getSourceClientOffset();
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      
      if (offset && canvasRect) {
        // 计算相对于画布的位置（转换为毫米）
        const x = (offset.x - canvasRect.left) * 0.264583; // px转mm
        const y = (offset.y - canvasRect.top) * 0.264583; // px转mm
        
        // 定义保护区域
        const TOP_PROTECTION = 35; // 顶部保护区域（mm）
        const PAGE_HEIGHT = 297; // A4纸高度（mm）
        const PAGE_MARGIN_BOTTOM = 20; // 底部边距（mm）
        const BOTTOM_PROTECTION = PAGE_HEIGHT - PAGE_MARGIN_BOTTOM; // 底部保护区域起始位置
        
        // 根据类型创建不同的默认内容
        let content;
        if (item.type === 'text') {
          content = '双击编辑文本';
        } else if (item.type === 'title') {
          content = '计算题';
        } else if (item.type === 'subtitle') {
          content = '方法总结';
        } else if (item.type === 'image') {
          content = item.content || 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII='; // 1x1透明图
        } else if (item.type === 'number') {
          content = '1';
        } else {
          content = { question: '双击编辑题目', options: [] };
        }
        
        console.log("Drop item:", item);
        
        // 计算元素高度
        let elementHeight;
        if (item.type.toLowerCase() === 'title') {
          elementHeight = 30;
        } else if (item.type.toLowerCase() === 'subtitle') {
          elementHeight = 20;
        } else if (item.type.toLowerCase() === 'image') {
          elementHeight = item.size?.height || 105;
        } else if (item.type.toLowerCase() === 'text') {
          // 对于文本元素使用更小的默认高度
          elementHeight = 15; // 默认使用单行文本高度
        } else {
          elementHeight = 15;
        }
        
        // 确保放置位置在保护区域内
        let safeY = y;
        
        // 检查顶部保护区域
        if (safeY < TOP_PROTECTION) {
          safeY = TOP_PROTECTION;
        }
        
        // 检查底部保护区域
        if (safeY + elementHeight > BOTTOM_PROTECTION) {
          safeY = BOTTOM_PROTECTION - elementHeight;
        }
        
        // 如果位置被调整，显示提示
        if (safeY !== y) {
          toast({
            title: "位置限制",
            description: "元素已自动调整位置以避开保护区域",
            status: "warning",
            duration: 2000,
            isClosable: true,
          });
        }
        
        // 创建新元素
        const newElement = {
          type: item.type.toLowerCase(),
          content,
          position: { 
            // 如果是序号组件，忽略横向位置，固定左对齐
            x: item.type.toLowerCase() === 'number' ? 20 : x, 
            y: safeY 
          },
          // 为文本元素添加默认尺寸
          size: item.type.toLowerCase() === 'text' ? { width: 170, height: 15 } : 
            (item.type === 'image' && item.size ? { ...item.size } : undefined)
        };
        
        // 直接添加元素，不进行碰撞检测
        addElement(newElement);
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });
  
  // 将毫米转换为像素
  const mmToPx = (mm: number) => mm * 3.7795275591;
  
  // 新增：判断元素在学生版中是否应该显示
  const shouldElementBeVisible = useCallback((element: Element, allElements: Element[]) => {
    // 教师版时显示所有元素
    if (documentVersion === 'teacher') {
      return true;
    }
    
    // 判断元素类型的辅助函数 - 使用严格字符串比较
    const isImageElement = (el: Element): boolean => el.type === 'image';
    const isNumberElement = (el: Element): boolean => {
      const isNumber = el.type === 'number';
      console.log(`判断元素[${el.id}]是否为编号元素: ${isNumber}`, {
        type: el.type,
        content: typeof el.content === 'string' ? el.content.substring(0, 10) : '非字符串内容'
      });
      return isNumber;
    };
    const isTitleElement = (el: Element): boolean => el.type === 'title';
    const isSubtitleElement = (el: Element): boolean => el.type === 'subtitle';
    
    // 输出元素类型和内容以便调试
    console.log(`检查元素可见性: id=${element.id}, type=${element.type}, y=${element.position.y}`, 
              { content: typeof element.content === 'string' ? element.content.substring(0, 20) : '非字符串内容' });
    
    // 学生版中始终显示图片元素
    if (isImageElement(element)) {
      return true;
    }
    
    // 学生版中始终显示标题和小标题
    if (isTitleElement(element) || isSubtitleElement(element)) {
      return true;
    }
    
    // 学生版中始终显示序号元素
    if (isNumberElement(element)) {
      return true;
    }
    
    // 找到所有序号元素并验证它们
    const numberElements = allElements.filter(el => {
      const isNumber = isNumberElement(el);
      if (isNumber) {
        console.log(`找到序号元素: id=${el.id}, position.y=${el.position.y}, content=${typeof el.content === 'string' ? el.content : 'non-string'}`);
      }
      return isNumber;
    });
    
    // 调试日志
    console.log(`页面包含 ${numberElements.length} 个序号元素`);
    
    // 如果页面没有序号元素，显示所有元素
    if (numberElements.length === 0) {
      console.log("页面没有序号元素，显示所有元素");
      return true;
    }
    
    // 如果页面只有一个序号元素，则隐藏序号之后的所有非图片、非标题、非小标题元素
    if (numberElements.length === 1) {
      const singleNumberElement = numberElements[0];
      
      // 调试日志
      console.log(`检查元素 ${element.id} 是否位于唯一序号 ${singleNumberElement.id} 之后`, {
        elementY: element.position.y,
        numberY: singleNumberElement.position.y,
        shouldHide: element.position.y > singleNumberElement.position.y
      });
      
      // 如果当前元素位置在序号之后且不是图片、标题或小标题，则隐藏
      if (element.position.y > singleNumberElement.position.y && 
          !isImageElement(element) && 
          !isTitleElement(element) && 
          !isSubtitleElement(element)) {
        console.log(`元素 ${element.id} 位于序号之后且不是保留类型，隐藏`);
        return false;
      }
    } else {
      // 如果有多个序号元素，按Y坐标排序
      const sortedNumberElements = [...numberElements].sort((a, b) => a.position.y - b.position.y);
      
      // 调试日志
      console.log("多个序号元素已排序", {
        sortedNumbers: sortedNumberElements.map(el => ({ id: el.id, y: el.position.y }))
      });
      
      // 检查当前元素是否在两个序号元素之间
      for (let i = 0; i < sortedNumberElements.length - 1; i++) {
        const currentNumber = sortedNumberElements[i];
        const nextNumber = sortedNumberElements[i + 1];
        
        // 调试日志
        console.log(`检查元素 ${element.id} 是否位于序号 ${currentNumber.id} 和 ${nextNumber.id} 之间`, {
          elementY: element.position.y,
          currentNumberY: currentNumber.position.y,
          nextNumberY: nextNumber.position.y,
          isBetween: element.position.y > currentNumber.position.y && element.position.y < nextNumber.position.y
        });
        
        // 如果元素在当前序号和下一个序号之间，且不是图片、标题或小标题，则隐藏
        if (
          element.position.y > currentNumber.position.y && 
          element.position.y < nextNumber.position.y && 
          !isImageElement(element) &&
          !isTitleElement(element) &&
          !isSubtitleElement(element)
        ) {
          console.log(`元素 ${element.id} 位于两个序号之间且不是保留类型，隐藏`);
          return false;
        }
      }
      
      // 检查是否在最后一个序号之后
      const lastNumberElement = sortedNumberElements[sortedNumberElements.length - 1];
      
      // 调试日志
      console.log(`检查元素 ${element.id} 是否位于最后一个序号 ${lastNumberElement.id} 之后`, {
        elementY: element.position.y,
        lastNumberY: lastNumberElement.position.y,
        isAfter: element.position.y > lastNumberElement.position.y
      });
      
      if (element.position.y > lastNumberElement.position.y && 
          !isImageElement(element) &&
          !isTitleElement(element) &&
          !isSubtitleElement(element)) {
        console.log(`元素 ${element.id} 位于最后一个序号之后且不是保留类型，隐藏`);
        return false;
      }
    }
    
    // 默认显示元素
    console.log(`元素 ${element.id} 未触发任何隐藏规则，显示`);
    return true;
  }, [documentVersion]);
  
  // 对元素进行排序，确保图片在文本下方渲染，实现文本绕排效果
  const sortedElements = [...currentPage.elements].sort((a, b) => {
    // 编号元素优先级最高，确保总是优先处理
    if (a.type === 'number' && b.type !== 'number') return -1;
    if (a.type !== 'number' && b.type === 'number') return 1;
    
    // 图片类型优先级较高，放在后面渲染（上层）
    if (a.type === 'image' && b.type !== 'image') return 1;
    if (a.type !== 'image' && b.type === 'image') return -1;
    
    // 同类型元素按Y坐标排序
    return a.position.y - b.position.y;
  });
  
  // 添加调试日志，显示排序后的元素
  console.log("排序后的元素列表:", sortedElements.map(el => ({
    id: el.id,
    type: el.type,
    y: el.position.y,
    content: typeof el.content === 'string' ? 
      (el.content.length > 20 ? el.content.substring(0, 20) + '...' : el.content) : 
      '非字符串内容'
  })));
  
  // 根据文档版本筛选需要显示的元素
  const visibleElements = sortedElements.filter(element => {
    const isVisible = shouldElementBeVisible(element, currentPage.elements);
    console.log(`元素 ${element.id} (${element.type}) 可见性结果: ${isVisible}`);
    return isVisible;
  });
  
  // 记录过滤后的可见元素
  console.log("过滤后的可见元素:", visibleElements.map(el => ({
    id: el.id,
    type: el.type,
    y: el.position.y
  })));
  
  // 确保渲染所有页面时应用相同的可见性规则
  useEffect(() => {
    // 当documentVersion状态变化时，强制重新渲染所有页面
    console.log(`文档版本切换到: ${documentVersion}，可见元素数量: ${visibleElements.length}`);
    
    // 检查是否有序号元素
    const numberElements = currentPage.elements.filter(el => el.type === 'number');
    console.log(`当前页面有 ${numberElements.length} 个序号元素，文档版本: ${documentVersion}`, {
      numberElements: numberElements.map(el => ({
        id: el.id,
        y: el.position.y,
        content: typeof el.content === 'string' ? el.content.substring(0, 10) : '非字符串内容'
      }))
    });
  }, [documentVersion, currentPage.elements, visibleElements.length]);
  
  return (
    <Box 
      p={4} 
      display="flex" 
      justifyContent="center" 
      alignItems="flex-start"
      minH="100%"
      bg="gray.100"
    >
      <Box
        position="relative"
        w={`${mmToPx(210)}px`}
        mt="60px"  // 增加顶部边距，为工具栏腾出空间
      >
        {/* 编辑工具栏 */}
        <Flex 
          position="absolute"
          top="-50px"  // 调整工具栏位置
          left="0"
          right="0"
          justifyContent="space-between"
          alignItems="center"
          zIndex={10}
        >
          {/* 左侧编辑按钮组 */}
          <HStack spacing={2}>
            <Tooltip label="撤销 (Ctrl+Z)">
              <IconButton
                aria-label="撤销"
                icon={<Icon as={FiRotateCcw} />}
                size="sm"
                colorScheme="blue"
                variant="outline"
                onClick={() => {
                  undo();
                  toast({
                    title: "撤销",
                    description: "已撤销上一步操作",
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Tooltip label="重做 (Ctrl+Y / Ctrl+Shift+Z)">
              <IconButton
                aria-label="重做"
                icon={<Icon as={FiRotateCw} />}
                size="sm"
                colorScheme="blue"
                variant="outline"
                onClick={() => {
                  redo();
                  toast({
                    title: "重做",
                    description: "已重做操作",
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Tooltip label="复制选中 (Ctrl+C)">
              <IconButton
                aria-label="复制"
                icon={<Icon as={FiCopy} />}
                size="sm"
                colorScheme="blue"
                variant="outline"
                isDisabled={selectedElementIds.length === 0}
                onClick={() => {
                  copySelectedElements();
                  toast({
                    title: "已复制",
                    description: `已复制 ${selectedElementIds.length} 个元素`,
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Tooltip label="剪切选中 (Ctrl+X)">
              <IconButton
                aria-label="剪切"
                icon={<Icon as={FiScissors} />}
                size="sm"
                colorScheme="blue"
                variant="outline"
                isDisabled={selectedElementIds.length === 0}
                onClick={() => {
                  cutSelectedElements();
                  toast({
                    title: "已剪切",
                    description: `已剪切 ${selectedElementIds.length} 个元素`,
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Tooltip label="粘贴 (Ctrl+V)">
              <IconButton
                aria-label="粘贴"
                icon={<Icon as={FiClipboard} />}
                size="sm"
                colorScheme="blue"
                variant="outline"
                onClick={() => {
                  pasteElements(mousePosition);
                  toast({
                    title: "已粘贴",
                    description: "已粘贴元素",
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Tooltip label="删除选中 (Delete)">
              <IconButton
                aria-label="删除"
                icon={<Icon as={FiTrash2} />}
                size="sm"
                colorScheme="red"
                variant="outline"
                isDisabled={selectedElementIds.length === 0}
                onClick={() => {
                  deleteSelectedElements();
                  toast({
                    title: "已删除",
                    description: "已删除选中元素",
                    status: "info",
                    duration: 1000,
                    isClosable: true,
                  });
                }}
              />
            </Tooltip>
            
            <Divider orientation="vertical" height="20px" />
            
            <Menu closeOnSelect={true}>
              <Tooltip label="对齐">
                <MenuButton 
                  as={IconButton}
                  aria-label="对齐"
                  icon={<Icon as={FiAlignLeft} />}
                  size="sm"
                  colorScheme="blue"
                  variant="outline"
                  isDisabled={selectedElementIds.length < 2}
                />
              </Tooltip>
              <MenuList>
                <MenuItem 
                  icon={<Icon as={FiAlignLeft} />} 
                  onClick={() => {
                    alignElements('left');
                    toast({
                      title: "左对齐",
                      description: "已将选中元素左对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  左对齐
                </MenuItem>
                <MenuItem 
                  icon={<Icon as={FiAlignCenter} />} 
                  onClick={() => {
                    alignElements('center');
                    toast({
                      title: "居中对齐",
                      description: "已将选中元素水平居中对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  居中对齐
                </MenuItem>
                <MenuItem 
                  icon={<Icon as={FiAlignRight} />} 
                  onClick={() => {
                    alignElements('right');
                    toast({
                      title: "右对齐",
                      description: "已将选中元素右对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  右对齐
                </MenuItem>
                <Divider />
                <MenuItem 
                  icon={<Icon as={BsArrowUp} />} 
                  onClick={() => {
                    alignElements('top');
                    toast({
                      title: "顶部对齐",
                      description: "已将选中元素顶部对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  顶部对齐
                </MenuItem>
                <MenuItem 
                  icon={<Icon as={BsArrowsExpand} />} 
                  onClick={() => {
                    alignElements('middle');
                    toast({
                      title: "垂直居中对齐",
                      description: "已将选中元素垂直居中对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  垂直居中
                </MenuItem>
                <MenuItem 
                  icon={<Icon as={BsArrowDown} />} 
                  onClick={() => {
                    alignElements('bottom');
                    toast({
                      title: "底部对齐",
                      description: "已将选中元素底部对齐",
                      status: "info",
                      duration: 1000,
                      isClosable: true,
                    });
                  }}
                >
                  底部对齐
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
          
          {/* 右侧布局按钮 - 修改为瀑布流布局按钮 */}
          <Tooltip label="重新排列所有元素，不限制每页题目数量">
            <Button
              size="sm"
              colorScheme="blue"
              leftIcon={<Icon as={BsGrid1X2} />}
              onClick={() => {
                // 临时保存当前的questionsPerPage值
                const currentQuestionsPerPage = questionsPerPage;
                
                // 设置为0（无限制）进行排列
                const setQuestionsPerPage = usePageStore.getState().setQuestionsPerPage;
                setQuestionsPerPage(0);
                
                // 执行排列
                arrangeElementsWaterfall();
                
                // 恢复原来的值
                setQuestionsPerPage(currentQuestionsPerPage);
                
                toast({
                  title: "排列完成",
                  description: "已重新排列所有元素，不限制每页题目数量",
                  status: "success",
                  duration: 2000,
                  isClosable: true,
                });
              }}
            >
              重新排列
            </Button>
          </Tooltip>
        </Flex>
        
        <Box
          ref={(node) => {
            canvasRef.current = node;
            dropRef(node);
          }}
          w={`${mmToPx(210)}px`}
          h={`${mmToPx(297)}px`}
          bg="white"
          boxShadow="md"
          position="relative"
          overflow="hidden"
          onClick={handleCanvasClick}
          tabIndex={0} // 使画布可聚焦以接收键盘事件
          data-canvas="true" // 添加数据属性以便PDF导出函数能够准确识别
          data-testid="canvas-content" // 添加测试ID，方便测试和识别
        >
          
          {/* 页眉 - 保留这部分，因为它是固定显示的 */}
          <DecorativeHeader 
            pageNumber={pages.indexOf(currentPage) + 1}
            customText={currentPage.header}
            color={currentPage.headerColor}
          />
          
          {/* 页面元素 - 使用过滤后的元素数组 */}
          {visibleElements.map(element => {
            console.log(`渲染元素:`, {
              id: element.id,
              type: element.type,
              isNumber: element.type === 'number',
              contentType: typeof element.content,
              hasSize: !!element.size
            });
            
            const isSelected = selectedElementIds.includes(element.id);
            
            if (element.type === 'number') {
              // 对编号元素添加额外的视觉提示，确保能被正确识别和渲染
              console.log(`渲染编号元素: ${element.id}`, {
                content: typeof element.content === 'string' ? element.content : '非字符串内容'
              });
            }
            
            if (element.type === 'image') {
              // 对图片元素进行可拖动包装
              return (
                <DraggableElement 
                  key={element.id} 
                  element={element} 
                  onDrag={handleElementDrag}
                  isSelected={isSelected}
                  onClick={(e) => handleElementClick(element.id, e)}
                >
                  <ImageElement element={element} isSelected={isSelected} />
                </DraggableElement>
              );
            }
            
            return (
              <DraggableElement 
                key={element.id} 
                element={element} 
                onDrag={handleElementDrag}
                isSelected={isSelected}
                onClick={(e) => handleElementClick(element.id, e)}
                data-element-type={element.type} // 添加数据属性
                data-element-id={element.id} // 添加ID数据属性便于调试
              >
                {element.type === 'text' ? (
                  <TextElement element={element} isSelected={isSelected} />
                ) : element.type === 'title' ? (
                  <TitleElement element={element} isSelected={isSelected} />
                ) : element.type === 'subtitle' ? (
                  <SubtitleElement element={element} isSelected={isSelected} />
                ) : element.type === 'number' ? (
                  <NumberElement element={element} isSelected={isSelected} />
                ) : (
                  <QuestionElement element={element} isSelected={isSelected} />
                )}
              </DraggableElement>
            );
          })}
          
          {/* 页脚 - 添加动态颜色 */}
          <DecorativeFooter 
            pageNumber={pages.indexOf(currentPage) + 1} 
            customText={currentPage.footer}
            color={currentPage.footerColor}
          />
          
          {/* 拖拽提示 */}
          {isOver && (
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              bg="blue.100"
              opacity={0.2}
              pointerEvents="none"
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Canvas;