import { PDFDocument, rgb } from 'pdf-lib';
import { Page } from '../store/pageStore';
import usePageStore from '../store/pageStore';

/**
 * TODO: [PDF导出功能改进计划] - 2023-06-05
 * 
 * 当前PDF导出功能存在以下需要解决的问题:
 * 
 * 1. 画布元素识别问题:
 *    - 无法准确定位A4画布元素，可能导致截取整个界面而非仅画布区域
 *    - 需要更精确的选择器或DOM遍历策略以定位真正的画布元素
 * 
 * 2. 导出图像质量问题:
 *    - 当前缩放和渲染参数可能导致导出的PDF不够清晰
 *    - 需要优化html2canvas配置以提高导出质量
 * 
 * 3. 中文字体渲染问题:
 *    - 当前使用的Base64嵌入字体可能不完整或加载不正确
 *    - 考虑使用更完整的字体或改进图像渲染方案
 * 
 * 4. 布局精度问题:
 *    - PDF中的元素位置与画布上显示的可能有差异
 *    - 需要确保坐标转换和缩放比例精确匹配
 * 
 * 后续改进计划:
 * - 实现更准确的画布元素定位方法
 * - 考虑使用WebGL或Canvas API直接渲染画布内容
 * - 研究更高效的PDF生成方案，可能使用服务器端渲染
 * 
 * @authors 初始实现者、后续贡献者
 */

// 毫米转PDF点数 (1mm = 2.83465点)
const mmToPt = (mm: number): number => mm * 2.83465;

// A4页面尺寸（毫米和点）
const A4_WIDTH_MM = 210;
const A4_HEIGHT_MM = 297;
const A4_WIDTH_PT = mmToPt(A4_WIDTH_MM);
const A4_HEIGHT_PT = mmToPt(A4_HEIGHT_MM);

// Noto Sans SC 字体的 Base64 格式（精简版，仅包含部分常用中文字符）
// 注意：实际项目中应该从CDN或本地资源加载完整字体
const NOTO_SANS_SC_REGULAR_BASE64 = 'AAEAAAASAQAABAAgR0RFRrRCtFQAAQuYAAACKkdQT1OmxoqKAAEOBAAAAphHU1VCgv9Z+gABGKAAAACgT1MvMoqVfSsAAR9AAAABVGNtYXABKwEUAAEglAAAAYxjdnQgK34EtQABIiAAAACMZnBnbYkPBBsAASlsAAAGxGdhc3AAAAAQAAEpXAAAAAhnbHlmrWaUlQAAAYAAAAdAaGVhZB+JLVcAASQ8AAAANmhoZWEHxgPdAAEkdAAAACRobXR4HAAAAQABJJgAAAAgbG9jYQXSBaYAASS4AAAAEm1heHADaQCOAAEkzAAAACBuYW1lu7pQNwABJOwAAAUrcG9zdP9tAGQAAS4YAAAAIHByZXAXLZg/AAEuOAAAANEAAQAAAAIAALsBMMBfDzz1AAIEOAAAAADU6VUKAAAAANTpVQoAAP++A6sD1gAAAAgAAgAAAAAAAAABAAAETP9pAIgEXQAAAAACZgABAAAAAAAAAAAAAAAAAAAACAABAAAACAAUAAUAAAAAAAIAAAABAAEAAABAAC4AAAAAAAQCMAD0AAUABgMZAZoAAAAUAwkBmgAAAJcFAQICAgICAgICAgICBQAAAAAAAAAAAAAAAEFOVCAAAAAgIQMETP9pAIgEXQCXIAACTfwAAAAB/AEqAAAAAAAAAAAAAAABAAEABAQEBA4EBA4EBA4EBA4EDgMxAxUCHwIHAeEBnwFnAUkBOAEvASsBJwEjAR8BGwEVAQgAxgCgAIQAdABeAEgANAAlABYACwAAABQrACsAKwArADIAMgAyADIAZABkAGQAZABkAGQAYQBhAGQAfAB8AIAAhQCoAKgAsQDSANIAzQDQANAA0ADRANIA0gDUANQA1ADUANcA1wDmAOUA5gDqAOsA6gD4AP0A/QEIAQkBEAENAQwBDAENAQ0BDQEJAP0A/QD3APcA9wD3APYA9gD3APUA9QD1APUA9QD1APQA9AD1APYA9gEBAPgBAAD+APAA8ADsAOoA+AEBAQQBEwESARgBFgEYARkBGQEJAO4A4QDbANsA2wDaANsA4QDbAN0A3QDiAOEA3wDkAOQA4wDkAOQA4QDeAN4A3wDWANcA0gDQANEAzgDTAMcAyQDEAMQAwQC+ALsAuQC0AK8AqwCmAKMAoQCdAJwAnACcAJwAmwCbAJsAmgCYAJgAlwCXAJYAlQCUAJMAkwCSAJAAjwCOAIsAhgCRAJIAkQCRAI8AkACOAIwAiwCJAJEAkQCSAJIAlQCVAJUAlQCVAJUAlgCWAJcAmACYAJgAmACYAJgAmACYAJgAmACZAJoAmgCbAJwAnQCdAJ0AnQCdAJ0AnQCdAJ0AnQCdAJ0AnQCdAJ0AnQCcAJsAmwCaAJkAlgCVAJUAlQCUAJQAkwCTAJMAkgCRAJEAjwCPAI8AjgCNAIwAjACMAIwAiwCKAIoAigCJAIgAiACIAIgAhwCGAIYAhgCFAIQAhACEAIQAgwCCAIIAgQCBAIAAiAAAABoAAAW4AAAAAAAAAxMAAAMTAJkEXQAA';

/**
 * 查找画布元素的多个可能选择器
 * @returns 找到的画布元素或null
 */
const findCanvasElement = (): HTMLElement | null => {
  console.log('开始寻找画布元素...');
  
  // 通过精确的CSS选择器直接定位Canvas组件中的A4画布元素
  // 对应Canvas.tsx中第619-629行的Box元素
  const canvasElement = document.querySelector('div[w][h][bg="white"][boxShadow="md"][position="relative"][overflow="hidden"]');
  if (canvasElement) {
    console.log('找到画布元素(A4画布)');
    return canvasElement as HTMLElement;
  }
  
  // 额外的Canvas选择器 - 更具体的尺寸匹配
  // 利用A4尺寸的特性(210mm x 297mm)进行匹配
  const a4Containers = document.querySelectorAll('div[style*="width"][style*="height"]');
  for (const container of Array.from(a4Containers)) {
    const style = window.getComputedStyle(container);
    const width = parseFloat(style.width);
    const height = parseFloat(style.height);
    
    // 检查是否符合A4比例(≈1:1.414)
    const aspectRatio = height / width;
    if (Math.abs(aspectRatio - 1.414) < 0.1 && style.backgroundColor === 'rgb(255, 255, 255)') {
      console.log(`找到A4比例容器: ${width}x${height}px, 比例: ${aspectRatio.toFixed(3)}`);
      return container as HTMLElement;
    }
  }
  
  // 回退到Canvas.tsx中定义的选择器，通过长宽属性匹配 
  // 查找指定宽高的容器元素(mmToPx(210) x mmToPx(297))
  const boxes = Array.from(document.querySelectorAll('.chakra-box')) as HTMLElement[];
  const a4CanvasElement = boxes.find(box => {
    const style = window.getComputedStyle(box);
    // 检查元素是否为A4尺寸的白色容器;
    return style.backgroundColor === 'rgb(255, 255, 255)' && 
           box.offsetWidth > 700 && box.offsetWidth < 850 && // A4宽度大约是793px (210mm)
           box.offsetHeight > 1000 && box.offsetHeight < 1200; // A4高度大约是1122px (297mm)
  });
  
  if (a4CanvasElement) {
    console.log(`找到A4画布元素: ${a4CanvasElement.offsetWidth}x${a4CanvasElement.offsetHeight}px`);
    return a4CanvasElement;
  }
  
  // 如果上述方法都失败，尝试通过直接引用Canvas组件的ref选择器
  // 在canvas元素上添加一个data属性，使得更容易被识别
  const canvasWithDataAttr = document.querySelector('[data-canvas="true"], [data-testid="canvas-content"]');
  if (canvasWithDataAttr) {
    console.log('通过数据属性找到画布元素');
    return canvasWithDataAttr as HTMLElement;
  }
  
  // 记录失败信息
  console.error('无法找到画布元素，将尝试在DOM中查找可能的画布。');
  
  // 最后的备选 - 查找具有特定特征的画布容器
  const mainContentArea = document.querySelector('div[area="main"] > div > div > div[bg="white"]');
  if (mainContentArea) {
    console.log('找到主内容区中的白色画布元素');
    return mainContentArea as HTMLElement; 
  }
  
  // 如果仍然找不到，使用main区域但不包括头部工具栏和其他UI元素
  const mainContent = document.querySelector('div[area="main"] > div > div');
  console.warn('无法精确定位画布，将使用主内容区域');
  return mainContent as HTMLElement;
};

/**
 * 将画布转换为图像
 * @param canvasElement 要转换的画布元素
 * @returns 包含图像数据的Promise
 */
const canvasToImage = async (canvasElement: HTMLElement): Promise<string> => {
  try {
    // 确保元素可见
    if (!canvasElement.offsetParent) {
      console.warn('画布元素不可见，可能影响截图质量');
    }

    console.log(`准备截图: 元素大小 ${canvasElement.offsetWidth}x${canvasElement.offsetHeight}`);
    
    // 使用html2canvas将DOM元素转换为Canvas
    // 需要动态导入html2canvas库，因为它只能在浏览器环境中使用
    const html2canvasModule = await import('html2canvas');
    const html2canvas = html2canvasModule.default;
    
    // html2canvas的TypeScript定义可能不完整，使用类型断言绕过检查
    const html2canvasOptions = {
      logging: false, // 减少日志输出
      allowTaint: true, // 允许使用有跨域元素
      useCORS: true, // 使用CORS加载图像
      backgroundColor: 'white', // 确保背景为白色
      removeContainer: false, // 保留容器避免影响布局
      // 以下参数在官方类型定义中可能缺失，但在实际库中可用
      scale: 2, // 提高图像质量
      windowWidth: canvasElement.offsetWidth,
      windowHeight: canvasElement.offsetHeight,
      width: canvasElement.offsetWidth,
      height: canvasElement.offsetHeight,
      x: 0,
      y: 0,
      letterRendering: true
    };
    
    // 使用类型断言告诉TypeScript我们知道这些配置是有效的
    const canvas = await html2canvas(canvasElement, html2canvasOptions as any);
    
    console.log(`截图完成: Canvas大小 ${canvas.width}x${canvas.height}`);
    
    // 转换为高质量的PNG数据URL
    return canvas.toDataURL('image/png', 1.0);
  } catch (error) {
    console.error('Canvas转图像失败:', error);
    throw new Error(`无法捕获页面图像: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * 导出页面为PDF
 * @param pages 要导出的页面数组
 * @returns 包含PDF数据的Promise
 */
export const exportToPDF = async (pages: Page[]): Promise<Uint8Array> => {
  try {
    // 创建新的PDF文档
    const pdfDoc = await PDFDocument.create();
    
    // 加载中文字体（从Base64字符串）
    console.log('加载中文字体...');
    let chineseFont;
    try {
      chineseFont = await pdfDoc.embedFont(NOTO_SANS_SC_REGULAR_BASE64);
      console.log('中文字体加载成功');
    } catch (fontError) {
      console.error('加载中文字体失败，将使用图像渲染:', fontError);
    }
    
    // 显示进度提示
    console.log('正在导出PDF...');
    
    // 保存当前页面ID，以便稍后恢复
    const pageStore = usePageStore.getState();
    const originalPageId = pageStore.currentPageId;
    
    // 对每个页面进行处理
    for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
      console.log(`处理页面 ${pageIndex + 1}/${pages.length}`);
      
      // 先切换到对应页面
      const page = pages[pageIndex];
      pageStore.setCurrentPage(page.id);
      
      // 等待页面渲染完成 - 增加等待时间，确保页面完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      try {
        // 尝试找到画布元素
        const canvasElement = findCanvasElement();
        if (!canvasElement) {
          throw new Error('无法找到画布元素');
        }
        
        // 等待一小段时间确保元素完全渲染
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 显示画布元素的尺寸信息
        console.log(`找到画布: ${canvasElement.offsetWidth}x${canvasElement.offsetHeight}px`);
        
        // 为确保捕获完整内容，先确保画布及其内容可见
        ensureElementVisible(canvasElement);
        
        // 在DOM渲染的下一帧截图，确保画布内容完整
        await new Promise(resolve => requestAnimationFrame(() => setTimeout(resolve, 300)));
        
        // 捕获当前可见的画布为图像
        const imageData = await canvasToImage(canvasElement);
        
        // 从Base64编码中提取图片数据
        const base64Data = imageData.split(',')[1];
        if (!base64Data) {
          throw new Error('无法提取图像数据');
        }
        
        // 解码base64数据
        const imageBytes = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
        
        // 将PNG图像嵌入到PDF中
        const embeddedImage = await pdfDoc.embedPng(imageBytes);
        
        // 创建新的A4页面
        const pdfPage = pdfDoc.addPage([A4_WIDTH_PT, A4_HEIGHT_PT]);
        
        // 获取图像尺寸
        const imgWidth = embeddedImage.width;
        const imgHeight = embeddedImage.height;
        
        // 计算缩放比例，以适应A4页面
        const scale = Math.min(
          A4_WIDTH_PT / imgWidth,
          A4_HEIGHT_PT / imgHeight
        );
        
        // 计算缩放后的尺寸
        const scaledWidth = imgWidth * scale;
        const scaledHeight = imgHeight * scale;
        
        // 在页面上绘制图像，居中对齐
        pdfPage.drawImage(embeddedImage, {
          x: (A4_WIDTH_PT - scaledWidth) / 2,
          y: (A4_HEIGHT_PT - scaledHeight) / 2,
          width: scaledWidth,
          height: scaledHeight,
        });
        
        console.log(`页面 ${pageIndex + 1} 添加到PDF成功`);
      } catch (error) {
        console.error(`处理页面 ${pageIndex + 1} 失败:`, error);
        throw error;
      }
    }
    
    // 恢复原始页面
    pageStore.setCurrentPage(originalPageId);
    
    // 序列化PDF并返回
    console.log('PDF生成完成，准备下载...');
    const pdfBytes = await pdfDoc.save();
    return pdfBytes;
  } catch (error) {
    console.error('PDF导出失败:', error);
    throw error;
  }
};

/**
 * 确保元素及其所有父元素可见
 * @param element 要确保可见的元素
 */
function ensureElementVisible(element: HTMLElement): void {
  // 当前元素
  const style = window.getComputedStyle(element);
  if (style.display === 'none') {
    element.style.display = 'block';
  }
  
  if (style.visibility === 'hidden') {
    element.style.visibility = 'visible';
  }
  
  // 确保元素有尺寸
  if (element.offsetWidth === 0 || element.offsetHeight === 0) {
    if (!element.style.width) element.style.width = 'auto';
    if (!element.style.height) element.style.height = 'auto';
  }
  
  // 确保父元素也可见
  let parent = element.parentElement;
  while (parent && parent !== document.body) {
    const parentStyle = window.getComputedStyle(parent);
    if (parentStyle.display === 'none') {
      parent.style.display = 'block';
    }
    if (parentStyle.visibility === 'hidden') {
      parent.style.visibility = 'visible';
    }
    parent = parent.parentElement;
  }
}