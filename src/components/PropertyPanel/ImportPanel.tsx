import React, { useRef, useState } from 'react';
import { Box, Button, VStack, useToast, Text, Progress } from '@chakra-ui/react';
import usePageStore, { Element } from '../../store/pageStore';
import mammoth from 'mammoth';
import { nanoid } from 'nanoid';
import { doElementsOverlap } from '../../utils/collisionDetection';

// mammoth.js类型扩展
interface MammothImage {
  read(format: string): Promise<string>;
  contentType?: string;
}

interface MammothConvertOptions {
  arrayBuffer: ArrayBuffer;
  convertImage: any;
}

// A4纸尺寸（毫米）
const PAGE_HEIGHT = 297; // A4页面高度（mm）
const PAGE_WIDTH = 210;  // A4页面宽度（mm）
const PAGE_MARGIN_TOP = 35; // 上边距（mm）- 增加页眉空间
const PAGE_MARGIN_BOTTOM = 20; // 下边距（mm）
const PAGE_CONTENT_HEIGHT = PAGE_HEIGHT - PAGE_MARGIN_TOP - PAGE_MARGIN_BOTTOM; // 页面内容区高度
const ELEMENT_SPACING = 2; // 元素之间的垂直间距（mm）- 减小为2mm与瀑布流设置保持一致
const TITLE_HEIGHT = 30; // 标题元素的高度估算（mm）
const TEXT_HEIGHT = 15;  // 普通文本元素的默认高度估算（mm）
const IMAGE_DEFAULT_HEIGHT = 50; // 图片默认高度（mm）

// 图片元素类型（如果在系统中尚未定义）
interface ImageElement {
  id: string;
  type: 'image';
  content: string; // base64编码的图片数据
  position: { x: number; y: number };
  size?: { width: number; height: number };
}

// 检测是否应该应用小标题样式的辅助函数
const shouldBeSubtitle = (text: string): boolean => {
  // 检查是否是使用#标记的小标题
  if (text.startsWith('#') && text.endsWith('#')) {
    return true;
  }
  
  // 删除原有检测逻辑，只保留#标记检测
  return false;
};

// 检测是否应该应用序号组件的辅助函数
const shouldBeNumber = (text: string): boolean => {
  // 检测是否以数字开头，紧跟着是点或顿号或空格
  const numberPattern = /^[0-9]+[.、\s]/;
  // 或者文本仅为单个数字（1-9）
  const singleNumberPattern = /^[0-9]$/;
  
  return numberPattern.test(text) || singleNumberPattern.test(text);
};

// 从文本中提取数字部分作为序号组件内容（用于兼容旧的处理方式）
const extractNumberContent = (text: string): string => {
  // 如果是单个数字，直接返回
  if (/^[0-9]$/.test(text)) {
    return text;
  }
  
  // 提取开头的数字部分
  const match = text.match(/^([0-9]+)[.、\s]/);
  if (match && match[1]) {
    return match[1];
  }
  
  // 默认返回1
  return "1";
};

// 从文本中提取非数字部分作为文本内容
const extractContentPart = (text: string): string => {
  // 如果是单个数字，返回空字符串
  if (/^[0-9]$/.test(text)) {
    return "";
  }
  
  // 去除开头的数字和分隔符，保留实际内容
  const match = text.match(/^[0-9]+[.、\s](.*)/);
  if (match && match[1]) {
    return match[1].trim();
  }
  
  // 默认返回空文本
  return "";
};

const ImportPanel = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const { addElement, currentPageId, addPage, setCurrentPage, pages } = usePageStore();
  const [isImporting, setIsImporting] = useState(false);
  
  // 获取当前页面上的所有元素
  const getCurrentPageElements = (): Element[] => {
    const currentPage = pages.find(page => page.id === currentPageId) || pages[0];
    return currentPage?.elements || [];
  };
  
  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    const file = e.target.files[0];
    setIsImporting(true);
    
    try {
      // 只处理DOCX文件
      if (file.name.endsWith('.docx')) {
        // 处理Word文档
        const arrayBuffer = await file.arrayBuffer();
        
        // 用于存储从Word提取的图片
        const extractedImages: { data: string; index: number }[] = [];
        let imageCounter = 0;
        
        // 使用mammoth提取Word文档内容
        const result = await mammoth.convertToHtml(
          { arrayBuffer },
          {
            convertImage: mammoth.images.imgElement(function(image: MammothImage) {
              return image.read("base64").then(function(imageBuffer: string) {
                // 创建完整的base64数据URL
                const contentType = image.contentType || 'image/png';
                const base64Data = `data:${contentType};base64,${imageBuffer}`;
                
                // 保存图片信息和在文档中的位置
                // 通过与图片在原始文档中的实际位置关联的索引
                extractedImages.push({
                  data: base64Data,
                  index: imageCounter // 使用imageCounter记录图片的原始顺序
                });
                
                // 增加计数器
                imageCounter++;
                
                // 返回图片的HTML占位符，添加特殊标记以便后续处理
                return {
                  src: base64Data,
                  class: `imported-image-${imageCounter - 1}` // 图片类名中包含索引
                };
              });
            })
          }
        );
        
        // 提取文本内容
        let { value: html } = result;
        
        // 将HTML转换为文本，保留基本结构
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 移除不需要的HTML元素
        const scripts = tempDiv.querySelectorAll('script, style');
        scripts.forEach(script => script.remove());
        
        // 获取文本段落和图片信息，保留它们的顺序关系
        // 使用混合内容数组记录段落和图片的原始顺序
        const mixedContents: Array<{
          type: 'text' | 'title' | 'subtitle' | 'image' | 'number';
          content: string;
          index: number; // 文档中的原始顺序
        }> = [];
        
        // 解析HTML并提取段落和图片位置关系
        let paragraphIndex = 0;
        
        // 处理tempDiv中的所有节点
        const processNode = (node: Node, index: number) => {
          if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent?.trim();
            if (text && text.length > 0) {
              mixedContents.push({
                type: 'text',
                content: text,
                index: index
              });
              paragraphIndex++;
            }
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            
            // 处理图片元素 - 从类名中提取图片索引
            if (element.tagName === 'IMG') {
              const className = element.className;
              const match = className.match(/imported-image-(\d+)/);
              if (match && match[1]) {
                const imageIndex = parseInt(match[1]);
                const imageData = extractedImages.find(img => img.index === imageIndex);
                
                if (imageData) {
                  mixedContents.push({
                    type: 'image',
                    content: imageData.data,
                    index: index // 使用相同的索引保持顺序
                  });
                }
              }
              return; // 不再处理图片的子节点
            }
            
            // 处理标题元素
            if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
              mixedContents.push({
                type: element.tagName === 'H1' ? 'title' : 'subtitle',
                content: element.textContent || '',
                index: index
              });
              paragraphIndex++;
              return; // 不再处理标题的子节点
            }
            
            // 处理段落元素
            if (element.tagName === 'P') {
              // 检查段落是否包含图片
              const hasImage = element.querySelector('img') !== null;
              
              if (!hasImage && element.textContent?.trim()) {
                // 纯文本段落
                const content = element.textContent.trim();
                const type = shouldBeNumber(content) ? 'number' : 'text';
                
                mixedContents.push({
                  type,
                  content,
                  index: index
                });
                paragraphIndex++;
              } else {
                // 段落包含图片，需要分别处理文本节点和图片节点
                let childIndex = index;
                Array.from(element.childNodes).forEach(childNode => {
                  processNode(childNode, childIndex);
                  childIndex += 0.1; // 使用小数增量保持子节点顺序
                });
              }
              return; // 已经处理了段落的子节点
            }
            
            // 递归处理其他元素的子节点
            let childIndex = index;
            Array.from(element.childNodes).forEach(childNode => {
              processNode(childNode, childIndex);
              childIndex += 0.1;
            });
          }
        };
        
        // 处理文档的主体内容
        Array.from(tempDiv.childNodes).forEach((node, idx) => {
          processNode(node, idx);
        });
        
        // 按原始顺序排序内容
        mixedContents.sort((a, b) => a.index - b.index);
        
        // 调整内容类型 - 第一个文本元素设为标题
        if (mixedContents.length > 0 && mixedContents[0].type === 'text') {
          mixedContents[0].type = 'title';
        }
        
        // 清理重复内容和空内容
        const filteredContents = mixedContents.filter((item, index, array) => {
          // 排除空内容
          if (typeof item.content === 'string' && item.content.trim() === '') {
            return false;
          }
          
          // 排除与前一项完全相同的内容（除了图片）
          if (index > 0 && item.type !== 'image' && array[index - 1].type !== 'image') {
            if (item.content === array[index - 1].content) {
              return false;
            }
          }
          
          return true;
        });
        
        console.log(`从Word文档中提取了文本和${extractedImages.length}张图片，共${filteredContents.length}个内容元素`);
        
        // 导入混合内容（文本和图片）
        importMixedContents(filteredContents);
      } else {
        toast({
          title: "不支持的文件类型",
          description: "请上传.docx文档",
          status: "error",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('导入文件时出错:', error);
      toast({
        title: "导入失败",
        description: "无法处理此文件，请确保文件格式正确",
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsImporting(false);
      // 重置input以允许再次选择相同文件
      e.target.value = '';
    }
  };
  
  // 使用函数来获取图片尺寸，可以在多个地方复用
  const getImageSize = (src: string): Promise<{ width: number, height: number }> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        // 将像素转换为毫米，假设96dpi (1px ≈ 0.264583mm)
        const mmPerPx = 0.264583;
        const widthMm = img.width * mmPerPx;
        const heightMm = img.height * mmPerPx;
        
        // 限制图片最大宽度为160mm（A4纸宽度的大约75%）
        const maxWidth = 160;
        
        // 如果图片宽度超过最大值，按比例缩小
        if (widthMm > maxWidth) {
          const aspectRatio = heightMm / widthMm;
          resolve({
            width: maxWidth,
            height: maxWidth * aspectRatio
          });
        } else {
          resolve({ 
            width: widthMm, 
            height: heightMm 
          });
        }
      };
      img.onerror = () => {
        // 图片加载失败时使用默认尺寸
        resolve({ width: 140, height: 105 });
      };
      img.src = src;
    });
  };

  const importMixedContents = (mixedContents: Array<{
    type: 'text' | 'title' | 'subtitle' | 'image' | 'number';
    content: string;
    index: number;
  }>) => {
    // 预处理：处理#标记的小标题，转换类型，但不添加任何元素
    const processedContents = mixedContents.map(item => {
      // 如果内容使用#标记为小标题
      if (item.type === 'text' && item.content.startsWith('#') && item.content.endsWith('#')) {
        return {
          ...item,
          type: 'subtitle' as const,
          content: item.content.substring(1, item.content.length - 1).trim()
        };
      }
      return item;
    });

    // 开始添加元素到页面
    let currentY = PAGE_MARGIN_TOP;
    let currentPageIndex = 0;
    let pageIds = [...pages.map(p => p.id)];
    let pagesCreated = 0;
    
    // 确保从第一页开始
    setCurrentPage(pages[0].id);
    
    // 处理每个元素
    const processNextElement = (elementIndex = 0) => {
      // 全部处理完毕
      if (elementIndex >= processedContents.length) {
        // 回到第一页，方便用户查看
        setTimeout(() => {
          setCurrentPage(pages[0].id);
          
          toast({
            title: "导入成功",
            description: `成功导入${processedContents.filter(el => el.type !== 'image').length}个文本段落和${processedContents.filter(el => el.type === 'image').length}张图片`,
            status: "success",
            duration: 3000,
          });
        }, 500);
        return;
      }
      
      const element = processedContents[elementIndex];
      
      // 获取当前页上所有的现有元素
      const currentPageElements = getCurrentPageElements();
      
      // 确保初始Y坐标不在页眉区域
      if (currentY < PAGE_MARGIN_TOP) {
        currentY = PAGE_MARGIN_TOP;
      }
      
      // 检查是否需要创建新页面
      if (element.type === 'image') {
        // 图片处理逻辑保持不变
        console.log(`添加图片到位置 (20, ${currentY})`);
        
        getImageSize(element.content).then(size => {
          if (currentY + size.height > PAGE_HEIGHT - PAGE_MARGIN_BOTTOM) {
            addPage();
            pagesCreated++;
            currentPageIndex++;
            
            if (currentPageIndex < pageIds.length) {
              setCurrentPage(pageIds[currentPageIndex]);
            } else if (pages.length > currentPageIndex) {
              setCurrentPage(pages[pages.length - 1].id);
              pageIds = [...pages.map(p => p.id)];
            }
            
            currentY = PAGE_MARGIN_TOP;
          }
          
          addElement({
            type: 'image',
            content: element.content,
            position: { x: 20, y: currentY },
            size: size
          });
          
          currentY += size.height + ELEMENT_SPACING;
          setTimeout(() => processNextElement(elementIndex + 1), 50);
        });
        
        return;
      } 
      
      // 非图片元素处理
      // 估算元素高度
      let elementHeight;
      let xPosition = 20; // 默认左对齐
      
      switch (element.type) {
        case 'title':
          elementHeight = TITLE_HEIGHT;
          xPosition = 55; // 标题居中
          break;
        case 'subtitle':
          elementHeight = TEXT_HEIGHT;
          break;
        case 'number':
          elementHeight = Math.min(30, 15 + Math.floor(element.content.length / 200) * 5);
          break;
        default: // text
          const calculatedHeight = TEXT_HEIGHT + Math.floor(element.content.length / 150) * 6.5;
          elementHeight = Math.min(30, calculatedHeight);
      }
      
      // 检查是否需要翻页
      if (currentY + elementHeight > PAGE_HEIGHT - PAGE_MARGIN_BOTTOM) {
        addPage();
        pagesCreated++;
        currentPageIndex++;
        
        setTimeout(() => {
          if (currentPageIndex < pageIds.length) {
            setCurrentPage(pageIds[currentPageIndex]);
          } else if (pages.length > currentPageIndex) {
            setCurrentPage(pages[pages.length - 1].id);
            pageIds = [...pages.map(p => p.id)];
          }
          
          currentY = PAGE_MARGIN_TOP;
          setTimeout(() => processNextElement(elementIndex), 100);
        }, 200);
        return;
      }
      
      // 添加元素到页面
      addElement({
        type: element.type,
        content: element.content,
        position: { 
          x: xPosition, 
          y: currentY
        }
      });
      
      // 更新Y坐标
      if (element.type === 'number') {
        // 序号元素添加额外间距
        currentY += elementHeight + ELEMENT_SPACING + 8;
      } else {
        // 其他元素使用标准间距
        currentY += elementHeight + ELEMENT_SPACING;
      }
      
      // 处理下一个元素
      setTimeout(() => processNextElement(elementIndex + 1), 50);
    };
    
    // 开始处理第一个元素
    processNextElement();
  };
  
  // 返回组件JSX
  return (
    <VStack spacing={2} align="stretch">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".docx"
        style={{ display: 'none' }}
      />
      <Button 
        onClick={handleImportClick} 
        colorScheme="blue" 
        size="sm" 
        width="100%" 
        isLoading={isImporting}
        loadingText="导入中..."
      >
        导入Word文档
      </Button>
      {isImporting && <Progress size="xs" isIndeterminate />}
      <Text fontSize="xs" color="gray.500">
        支持.docx(Word)文件，包括Word中的图片
      </Text>
    </VStack>
  );
};

export default ImportPanel;