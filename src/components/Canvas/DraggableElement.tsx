import React, { useRef, useState, useCallback } from 'react';
import { Box } from '@chakra-ui/react';
import { useDrag } from 'react-dnd';
import usePageStore, { Element } from '../../store/pageStore';

interface DraggableElementProps {
  element: Element;
  children: React.ReactNode;
  onDrag?: (id: string, position: { x: number, y: number }) => void;
  isSelected?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  [key: string]: any; // 添加剩余属性支持
}

const DraggableElement: React.FC<DraggableElementProps> = ({ 
  element, 
  children, 
  onDrag,
  isSelected = false,
  onClick,
  ...restProps // 收集其余属性
}) => {
  const { updateElement } = usePageStore();
  const [nodeRef, setNodeRef] = useState<HTMLDivElement | null>(null);
  
  const [{ isDragging }, drag] = useDrag({
    type: 'CANVAS_ELEMENT',
    item: () => ({ id: element.id }),
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const delta = monitor.getDifferenceFromInitialOffset();
      
      if (delta && nodeRef) {
        // 计算新位置（毫米）
        const mmPerPx = 0.264583; // 1px ≈ 0.264583mm
        const newX = element.position.x + (delta.x * mmPerPx);
        const newY = element.position.y + (delta.y * mmPerPx);
        
        // 使用自定义回调或默认行为
        if (onDrag) {
          onDrag(element.id, { x: newX, y: newY });
        } else {
          updateElement(element.id, {
            position: { x: newX, y: newY }
          });
        }
      }
    },
  });
  
  // 对于标题组件，我们需要特殊处理位置和宽度
  const elementStyle = {};
  
  // 根据元素类型设置位置
  const position = element.type === 'title' 
    ? { top: '20mm', left: '50%', transform: 'translateX(-50%)' }
    : element.type === 'subtitle'
    ? { top: `${element.position.y}mm`, left: '20mm' }
    : element.type === 'number'
    ? { top: `${element.position.y}mm`, left: '20mm', width: '160mm' } // 序号组件固定左对齐且设置宽度
    : { left: `${element.position.x}mm`, top: `${element.position.y}mm` };
  
  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick(e);
    }
  };
  
  // 合并 ref 处理
  const refCallback = useCallback((node: HTMLDivElement | null) => {
    // 设置节点状态
    setNodeRef(node);
    // 应用 drag ref
    drag(node);
  }, [drag]);
  
  return (
    <Box
      ref={refCallback}
      position="absolute"
      {...position}
      cursor={isDragging ? 'grabbing' : 'grab'}
      opacity={isDragging ? 0.5 : 1}
      border={isSelected ? '2px solid blue' : '1px solid transparent'}
      onClick={handleClick}
      tabIndex={0}
      zIndex={isSelected ? 2 : 1}
      boxShadow={isSelected ? 'lg' : 'none'}
      data-element-type={element.type} // 添加类型数据属性
      data-element-id={element.id} // 添加ID数据属性
      {...restProps} // 传递剩余属性
    >
      {children}
    </Box>
  );
};

export default DraggableElement;