import { v4 as uuidv4 } from 'uuid';
import { nanoid } from 'nanoid';

// 定义元素接口
export interface Element {
  id: string;
  type: 'text' | 'question' | 'title' | 'subtitle' | 'image' | 'number';
  content: string | Record<string, any>;  // 改进类型定义，不再使用any
  position: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  fontSize?: 'small' | 'medium' | 'large';
}

// 定义页面接口
export interface Page {
  id: string;
  elements: Element[];
  header?: string;
  footer?: string;
  headerColor?: string;
  footerColor?: string;
}

// 定义历史记录的操作类型
export type HistoryAction = {
  type: 'add' | 'update' | 'delete' | 'arrangement';
  pageId: string;
  elements: Element[];
  previousElements?: Element[];
};

// 生成唯一ID的工具函数
export const generateId = () => nanoid();

// 创建新页面的工厂函数
export const createNewPage = (): Page => ({
  id: uuidv4(),
  elements: [],
  header: '页面 {{pageNumber}}',
  footer: '{{pageNumber}}',
  headerColor: 'orange.300',
  footerColor: 'orange.300'
});

// 创建新元素的工厂函数
export const createNewElement = (elementData: Omit<Element, 'id'>): Element => ({
  ...elementData,
  id: generateId()
}); 