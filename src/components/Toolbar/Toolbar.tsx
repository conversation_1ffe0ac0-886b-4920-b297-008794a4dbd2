import React, { useState } from 'react';
import { Flex, <PERSON>ton, Divider, HStack, IconButton, Tooltip, useToast, Switch, FormControl, FormLabel, Box, Text } from '@chakra-ui/react';
import { AddIcon, DeleteIcon, DownloadIcon, ViewIcon, ViewOffIcon } from '@chakra-ui/icons';
import { BsListOl } from 'react-icons/bs';
import { FiList } from 'react-icons/fi';
import usePageStore, { Element } from '../../store/pageStore';
import { exportToPDF } from '../../utils/pdfExport';

const Toolbar = () => {
  const { pages, currentPageId, addPage, deletePage, documentVersion, setDocumentVersion, addElement } = usePageStore();
  const toast = useToast();
  const [isExporting, setIsExporting] = useState(false);
  
  const handleExport = async () => {
    if (isExporting) return; // 防止重复点击
    
    setIsExporting(true);
    toast({
      title: "正在准备PDF",
      description: "正在生成PDF文件，请稍候...",
      status: "info",
      duration: 2000,
      isClosable: true,
    });
    
    try {
      const pdfBytes = await exportToPDF(pages);
      
      // 创建下载链接
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '排版文档.pdf';
      link.click();
      
      // 清理
      URL.revokeObjectURL(url);
      
      toast({
        title: "导出成功",
        description: "PDF文件已成功生成并下载",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('导出PDF失败:', error);
      
      let errorMessage = "导出PDF时发生错误";
      
      // 如果是图片处理错误
      if (error.message && error.message.includes("图片")) {
        errorMessage = "导出PDF失败：图片处理错误。请检查图片格式是否支持。";
      } 
      // 如果包含详细错误信息
      else if (error.message) {
        errorMessage = `导出PDF失败：${error.message}`;
      }
      
      toast({
        title: "导出失败",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsExporting(false);
    }
  };
  
  // 切换文档版本
  const toggleDocumentVersion = () => {
    const newVersion = documentVersion === 'teacher' ? 'student' : 'teacher';
    setDocumentVersion(newVersion);
    
    toast({
      title: `切换到${newVersion === 'teacher' ? '教师版' : '学生版'}`,
      description: `已将所有页面切换为${newVersion === 'teacher' ? '教师版' : '学生版'}`,
      status: "info",
      duration: 2000,
      isClosable: true,
    });
  };
  
  // 添加编号元素
  const addNumberElement = () => {
    const currentPage = pages.find(page => page.id === currentPageId);
    if (!currentPage) return;
    
    // 找到当前页面的所有编号元素
    const numberElements = currentPage.elements.filter(el => el.type === 'number');
    
    // 确定新编号的序号
    let nextNumber = 1;
    if (numberElements.length > 0) {
      // 尝试从现有编号元素中找出最大的数字
      const existingNumbers = numberElements
        .map(el => {
          const match = String(el.content).match(/^(\d+)/);
          return match ? parseInt(match[1]) : 0;
        })
        .filter(num => !isNaN(num));
      
      if (existingNumbers.length > 0) {
        nextNumber = Math.max(...existingNumbers) + 1;
      }
    }
    
    // 创建新的编号元素，确保类型与接口匹配
    const newNumberElement: Omit<Element, 'id'> = {
      type: 'number', // 使用字面量类型
      content: `${nextNumber}. 新编号项`,
      position: { 
        x: 20, // 固定左对齐
        y: numberElements.length > 0 ? 
           // 放在最后一个编号元素下方
           Math.max(...numberElements.map(el => el.position.y)) + 30 : 
           // 或在页面上方的某个位置
           80
      },
      fontSize: 'medium' // 添加字体大小
    };
    
    // 添加到当前页面
    addElement(newNumberElement);
    
    // 显示提示
    toast({
      title: "添加编号成功",
      description: `已添加编号 ${nextNumber}`,
      status: "success",
      duration: 2000,
      isClosable: true,
    });
  };
  
  return (
    <Flex 
      p={2} 
      bg="white" 
      borderBottomWidth={1} 
      borderColor="gray.200"
      alignItems="center"
      justifyContent="space-between"
    >
      <HStack spacing={2}>
        <Tooltip label="添加页面">
          <IconButton
            aria-label="添加页面"
            icon={<AddIcon />}
            onClick={addPage}
          />
        </Tooltip>
        
        <Tooltip label="删除当前页面">
          <IconButton
            aria-label="删除页面"
            icon={<DeleteIcon />}
            onClick={() => deletePage(currentPageId)}
            isDisabled={pages.length <= 1}
          />
        </Tooltip>
        
        <Divider orientation="vertical" h="24px" />
        
        <Tooltip label={isExporting ? "导出中..." : "导出PDF"}>
          <IconButton
            aria-label="导出PDF"
            icon={<DownloadIcon />}
            onClick={handleExport}
            isLoading={isExporting}
          />
        </Tooltip>
      </HStack>
      
      {/* 版本切换部分 */}
      <HStack spacing={2}>
        <FormControl display="flex" alignItems="center">
          <FormLabel htmlFor="version-switch" mb="0" fontSize="sm">
            <Text color={documentVersion === 'teacher' ? 'blue.500' : 'gray.500'} fontWeight="bold">
              教师版
            </Text>
          </FormLabel>
          <Switch 
            id="version-switch" 
            isChecked={documentVersion === 'student'}
            onChange={toggleDocumentVersion}
            colorScheme="green"
          />
          <FormLabel htmlFor="version-switch" mb="0" ml={2} fontSize="sm">
            <Text color={documentVersion === 'student' ? 'green.500' : 'gray.500'} fontWeight="bold">
              学生版
            </Text>
          </FormLabel>
        </FormControl>
        
        <Tooltip label={documentVersion === 'teacher' ? "切换到学生版" : "切换到教师版"}>
          <IconButton
            aria-label="切换版本"
            icon={documentVersion === 'teacher' ? <ViewOffIcon /> : <ViewIcon />}
            onClick={toggleDocumentVersion}
            colorScheme={documentVersion === 'teacher' ? "blue" : "green"}
          />
        </Tooltip>
      </HStack>
    </Flex>
  );
};

export default Toolbar;