import React from 'react';
import { Box, Text, Flex, Circle } from '@chakra-ui/react';

interface DecorativeFooterProps {
  pageNumber: number;
  customText?: string;
  color?: string;
}

const DecorativeFooter: React.FC<DecorativeFooterProps> = ({ pageNumber, customText, color = "yellow.200" }) => {
  return (
    <Box width="100%" position="absolute" bottom="0" left="0">
      {/* 自定义文本区域 */}
      {customText && (
        <Flex 
          position="absolute" 
          bottom="5mm" 
          left="0" 
          right="0" 
          justifyContent="center" 
          alignItems="center"
          zIndex={2}
        >
          <Box position="relative" display="inline-block">
            <Circle 
              size="10mm" 
              bg={color} 
              position="absolute"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              zIndex={0}
              borderWidth="1.5mm"
              borderColor="white"
            />
            <Text 
              fontWeight="medium" 
              position="relative" 
              zIndex={1} 
              px={6} 
              py={2}
              textAlign="center"
            >
              {customText.replace('{{pageNumber}}', String(pageNumber))}
            </Text>
          </Box>
        </Flex>
      )}
      
      {/* 动态背景颜色 */}
      <Box bg={color} height="10mm" width="100%" position="relative" zIndex={1}>
      </Box>
    </Box>
  );
};

export default DecorativeFooter;